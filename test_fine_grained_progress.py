#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试精细化进度跟踪逻辑
模拟你描述的场景：
- 自动回复: 450/500 (进行中)
- 自动回评: 500/500 (完成，可以开始新批次)
- 自动出餐: 450/500 (进行中)
"""

import sys
import os
import threading
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routes.task_routes import (
    is_executor_busy, 
    get_executor_status,
    reply_executor, 
    comment_executor, 
    meal_executor,
    task_progress,
    task_progress_lock
)

def simulate_task_progress():
    """模拟任务进度状态"""
    print("🎭 模拟任务进度状态...")
    
    with task_progress_lock:
        # 模拟你描述的场景
        task_progress['auto_reply']['total'] = 500
        task_progress['auto_reply']['completed'] = 450  # 进行中
        
        task_progress['auto_comment']['total'] = 500
        task_progress['auto_comment']['completed'] = 500  # 已完成
        
        task_progress['auto_meal']['total'] = 500
        task_progress['auto_meal']['completed'] = 450  # 进行中
    
    print("📊 当前任务进度:")
    with task_progress_lock:
        print(f"  自动回复: {task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']}")
        print(f"  自动回评: {task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']}")
        print(f"  自动出餐: {task_progress['auto_meal']['completed']}/{task_progress['auto_meal']['total']}")

def test_dispatch_logic():
    """测试分发逻辑"""
    print("\n🔍 测试分发逻辑...")
    
    # 检查各个任务类型是否可以分发
    with task_progress_lock:
        # 自动回复：450/500，线程池空闲，但任务未完成 -> 不能分发
        reply_can_dispatch = (task_progress['auto_reply']['total'] == 0 or 
            (not is_executor_busy(reply_executor) and 
             task_progress['auto_reply']['completed'] >= task_progress['auto_reply']['total']))
        
        # 自动回评：500/500，线程池空闲，任务已完成 -> 可以分发
        comment_can_dispatch = (task_progress['auto_comment']['total'] == 0 or 
            (not is_executor_busy(comment_executor) and 
             task_progress['auto_comment']['completed'] >= task_progress['auto_comment']['total']))
        
        # 自动出餐：450/500，线程池空闲，但任务未完成 -> 不能分发
        meal_can_dispatch = (task_progress['auto_meal']['total'] == 0 or 
            (not is_executor_busy(meal_executor) and 
             task_progress['auto_meal']['completed'] >= task_progress['auto_meal']['total']))
    
    print(f"📤 分发检查结果:")
    print(f"  自动回复可以分发: {reply_can_dispatch} (原因: {'任务未完成' if not reply_can_dispatch else '可以分发'})")
    print(f"  自动回评可以分发: {comment_can_dispatch} (原因: {'任务已完成，可以开始新批次' if comment_can_dispatch else '不能分发'})")
    print(f"  自动出餐可以分发: {meal_can_dispatch} (原因: {'任务未完成' if not meal_can_dispatch else '可以分发'})")
    
    return reply_can_dispatch, comment_can_dispatch, meal_can_dispatch

def simulate_comment_new_batch():
    """模拟自动回评开始新批次"""
    print("\n🔄 模拟自动回评开始新批次...")
    
    with task_progress_lock:
        # 重置自动回评计数器，开始新批次
        if task_progress['auto_comment']['completed'] >= task_progress['auto_comment']['total']:
            print(f"🔄 自动回评批次完成，准备开始新批次: {task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']}")
            task_progress['auto_comment']['total'] = 0
            task_progress['auto_comment']['completed'] = 0
            
            # 模拟分发新的300个任务
            task_progress['auto_comment']['total'] = 300
            print(f"📤 分发自动回评任务: 300 个店铺")
    
    print("📊 新批次后的任务进度:")
    with task_progress_lock:
        print(f"  自动回复: {task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']} (继续进行中)")
        print(f"  自动回评: {task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']} (新批次开始)")
        print(f"  自动出餐: {task_progress['auto_meal']['completed']}/{task_progress['auto_meal']['total']} (继续进行中)")

def simulate_tasks_completion():
    """模拟任务完成"""
    print("\n⏰ 模拟一段时间后，自动回复和自动出餐也完成了...")
    
    with task_progress_lock:
        # 模拟自动回复和自动出餐完成
        task_progress['auto_reply']['completed'] = 500
        task_progress['auto_meal']['completed'] = 500
    
    print("📊 任务完成后的进度:")
    with task_progress_lock:
        print(f"  自动回复: {task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']} (已完成)")
        print(f"  自动回评: {task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']} (新批次进行中)")
        print(f"  自动出餐: {task_progress['auto_meal']['completed']}/{task_progress['auto_meal']['total']} (已完成)")
    
    # 再次检查分发逻辑
    print("\n🔍 再次检查分发逻辑...")
    reply_can_dispatch, comment_can_dispatch, meal_can_dispatch = test_dispatch_logic()
    
    print(f"\n📋 此时的分发状态:")
    print(f"  自动回复: {'✅ 可以开始新批次' if reply_can_dispatch else '❌ 不能分发'}")
    print(f"  自动回评: {'❌ 当前批次进行中' if not comment_can_dispatch else '✅ 可以分发'}")
    print(f"  自动出餐: {'✅ 可以开始新批次' if meal_can_dispatch else '❌ 不能分发'}")

def main():
    """主测试函数"""
    print("🚀 测试精细化进度跟踪逻辑")
    print("=" * 60)
    
    # 1. 模拟初始状态
    simulate_task_progress()
    
    # 2. 测试分发逻辑
    reply_can_dispatch, comment_can_dispatch, meal_can_dispatch = test_dispatch_logic()
    
    # 3. 模拟自动回评开始新批次
    if comment_can_dispatch:
        simulate_comment_new_batch()
    
    # 4. 模拟其他任务完成
    simulate_tasks_completion()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    
    print("\n📋 逻辑总结:")
    print("1. 每种任务独立检查是否可以分发新批次")
    print("2. 只有当前批次完成且线程池空闲时，才能开始新批次")
    print("3. 不同任务类型可以处于不同的执行阶段")
    print("4. 进度跟踪精确反映每种任务的执行状态")
    print("5. 实现了你描述的精细化调度逻辑")

if __name__ == "__main__":
    main()
