#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实时带宽监控工具
监控任务分发的带宽使用情况，帮助调优参数
"""

import sys
import os
import time
import threading
from collections import defaultdict, deque
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class BandwidthMonitor:
    def __init__(self):
        self.bandwidth_log = deque(maxlen=60)  # 保存60秒的数据
        self.current_bandwidth = 0
        self.peak_bandwidth = 0
        self.total_requests = 0
        self.lock = threading.Lock()
        
    def record_request(self, size_kb=2):
        """记录一个请求"""
        with self.lock:
            current_time = time.time()
            self.bandwidth_log.append((current_time, size_kb))
            self.current_bandwidth += size_kb
            self.total_requests += 1
            
            if self.current_bandwidth > self.peak_bandwidth:
                self.peak_bandwidth = self.current_bandwidth
    
    def get_current_stats(self):
        """获取当前统计信息"""
        with self.lock:
            current_time = time.time()
            
            # 清理1秒前的数据
            while self.bandwidth_log and current_time - self.bandwidth_log[0][0] > 1:
                old_time, old_size = self.bandwidth_log.popleft()
                self.current_bandwidth -= old_size
            
            # 计算最近10秒的平均带宽
            recent_bandwidth = 0
            recent_count = 0
            for log_time, size in self.bandwidth_log:
                if current_time - log_time <= 10:
                    recent_bandwidth += size
                    recent_count += 1
            
            avg_bandwidth = recent_bandwidth / 10 if recent_count > 0 else 0
            
            return {
                'current': self.current_bandwidth,
                'peak': self.peak_bandwidth,
                'average_10s': avg_bandwidth,
                'total_requests': self.total_requests
            }
    
    def reset_stats(self):
        """重置统计信息"""
        with self.lock:
            self.bandwidth_log.clear()
            self.current_bandwidth = 0
            self.peak_bandwidth = 0
            self.total_requests = 0

def simulate_task_dispatch():
    """模拟任务分发过程"""
    print("🎭 模拟任务分发过程")
    print("=" * 60)
    
    try:
        from routes.task_routes import (
            REPLY_BATCH_INTERVAL,
            COMMENT_BATCH_INTERVAL, 
            MEAL_BATCH_INTERVAL,
            reply_executor,
            comment_executor,
            meal_executor
        )
        
        monitor = BandwidthMonitor()
        
        print(f"📋 当前配置:")
        print(f"  自动回复: {REPLY_BATCH_INTERVAL}秒间隔, {reply_executor._max_workers}线程")
        print(f"  自动回评: {COMMENT_BATCH_INTERVAL}秒间隔, {comment_executor._max_workers}线程")
        print(f"  自动出餐: {MEAL_BATCH_INTERVAL}秒间隔, {meal_executor._max_workers}线程")
        
        print(f"\n⏱️ 开始模拟 (30秒)...")
        print(f"{'时间':<6} {'当前带宽':<10} {'峰值带宽':<10} {'平均带宽':<10} {'可视化'}")
        print("-" * 60)
        
        start_time = time.time()
        
        # 模拟分发线程
        def dispatch_replies():
            for i in range(reply_executor._max_workers):
                time.sleep(i * REPLY_BATCH_INTERVAL)
                # 模拟每批次处理33个店铺 (100/3)
                for _ in range(33):
                    monitor.record_request(2)  # 2KB per request
        
        def dispatch_comments():
            for i in range(comment_executor._max_workers):
                time.sleep(i * COMMENT_BATCH_INTERVAL)
                # 模拟每批次处理100个店铺
                for _ in range(100):
                    monitor.record_request(2)
        
        def dispatch_meals():
            for i in range(meal_executor._max_workers):
                time.sleep(i * MEAL_BATCH_INTERVAL)
                # 模拟每批次处理10个店铺 (100/10)
                for _ in range(10):
                    monitor.record_request(2)
        
        # 启动模拟线程
        threading.Thread(target=dispatch_replies, daemon=True).start()
        threading.Thread(target=dispatch_comments, daemon=True).start()
        threading.Thread(target=dispatch_meals, daemon=True).start()
        
        # 监控30秒
        for second in range(30):
            time.sleep(1)
            stats = monitor.get_current_stats()
            
            elapsed = second + 1
            current_bw = stats['current']
            peak_bw = stats['peak']
            avg_bw = stats['average_10s']
            
            # 可视化当前带宽
            max_display = 200  # 最大显示带宽
            bar_length = min(int((current_bw / max_display) * 20), 20)
            bar = "█" * bar_length
            
            print(f"{elapsed:<6} {current_bw:<10.1f} {peak_bw:<10.1f} {avg_bw:<10.1f} {bar}")
        
        # 最终统计
        final_stats = monitor.get_current_stats()
        print(f"\n📊 最终统计:")
        print(f"  峰值带宽: {final_stats['peak']:.1f}KB")
        print(f"  平均带宽: {final_stats['average_10s']:.1f}KB")
        print(f"  总请求数: {final_stats['total_requests']}")
        
        if final_stats['peak'] > 0:
            smoothness = final_stats['average_10s'] / final_stats['peak']
            print(f"  平滑度: {smoothness:.2f} (越接近1越平滑)")
            
            if smoothness > 0.7:
                print(f"  ✅ 带宽使用很平滑")
            elif smoothness > 0.4:
                print(f"  ⚖️ 带宽使用较平滑")
            else:
                print(f"  ⚠️ 带宽使用不够平滑，建议增加间隔")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        return False

def compare_configurations():
    """比较不同配置的效果"""
    print(f"\n🔄 配置对比分析")
    print("=" * 60)
    
    configs = [
        ("当前配置", {"reply": 1.0, "comment": 0.6, "meal": 1.5}),
        ("快速配置", {"reply": 0.3, "comment": 0.2, "meal": 0.5}),
        ("均衡配置", {"reply": 0.5, "comment": 0.3, "meal": 0.8}),
        ("慢速配置", {"reply": 1.5, "comment": 1.0, "meal": 2.0})
    ]
    
    print(f"{'配置':<12} {'分发时间':<10} {'峰值估算':<10} {'平滑度':<8} {'推荐场景'}")
    print("-" * 60)
    
    for name, config in configs:
        # 计算分发时间
        dispatch_time = max(
            3 * config['reply'],      # 3个回复线程
            1 * config['comment'],    # 1个回评线程
            10 * config['meal']       # 10个出餐线程
        )
        
        # 估算峰值 (假设100个店铺，每个2KB)
        # 峰值出现在0秒时刻，所有类型同时开始
        peak_estimate = (33 + 100 + 10) * 2  # 143个请求 * 2KB
        
        # 计算平滑度 (基于分发时间)
        if dispatch_time > 0:
            smoothness = min(1.0, 5.0 / dispatch_time)  # 5秒作为基准
        else:
            smoothness = 0
        
        # 推荐场景
        if dispatch_time < 3:
            scenario = "高性能网络"
        elif dispatch_time < 8:
            scenario = "一般网络"
        elif dispatch_time < 15:
            scenario = "低带宽网络"
        else:
            scenario = "极低带宽"
        
        print(f"{name:<12} {dispatch_time:<10.1f} {peak_estimate:<10.1f} {smoothness:<8.2f} {scenario}")
    
    print(f"\n💡 选择建议:")
    print(f"  - 如果网络带宽充足且要求响应快: 选择快速配置")
    print(f"  - 如果网络一般且要求均衡: 选择均衡配置")
    print(f"  - 如果网络带宽有限: 选择当前配置或慢速配置")
    print(f"  - 如果要求极致平滑: 选择慢速配置")

def generate_config_code():
    """生成配置代码"""
    print(f"\n📝 配置代码生成")
    print("=" * 60)
    
    configs = {
        "极致平滑": {"reply": 1.5, "comment": 1.0, "meal": 2.0},
        "当前配置": {"reply": 1.0, "comment": 0.6, "meal": 1.5},
        "均衡使用": {"reply": 0.5, "comment": 0.3, "meal": 0.8},
        "高性能": {"reply": 0.3, "comment": 0.2, "meal": 0.5}
    }
    
    print(f"选择以下配置之一，复制到 routes/task_routes.py 中:")
    
    for name, config in configs.items():
        print(f"\n# {name}配置")
        print(f"REPLY_BATCH_INTERVAL = {config['reply']}")
        print(f"COMMENT_BATCH_INTERVAL = {config['comment']}")
        print(f"MEAL_BATCH_INTERVAL = {config['meal']}")

def main():
    """主函数"""
    print("🚀 带宽监控和优化工具")
    
    # 1. 模拟任务分发
    simulate_task_dispatch()
    
    # 2. 配置对比
    compare_configurations()
    
    # 3. 生成配置代码
    generate_config_code()
    
    print("\n" + "=" * 60)
    print("✅ 监控完成")
    
    print(f"\n🎯 优化建议:")
    print(f"1. 当前配置已经比较平滑，峰谷比应该大幅改善")
    print(f"2. 如果仍有峰值问题，可以选择'极致平滑'配置")
    print(f"3. 可以根据实际网络情况微调参数")
    print(f"4. 建议在实际环境中测试并观察带宽使用情况")

if __name__ == "__main__":
    main()
