#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试优化后的调度器逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routes.task_routes import (
    is_executor_busy, 
    get_executor_status,
    reply_executor, 
    comment_executor, 
    meal_executor,
    task_progress,
    task_progress_lock
)

def test_executor_status():
    """测试线程池状态检查"""
    print("=== 线程池状态测试 ===")
    
    # 测试各个线程池的状态
    reply_status = get_executor_status(reply_executor, '自动回复')
    comment_status = get_executor_status(comment_executor, '自动回评')
    meal_status = get_executor_status(meal_executor, '自动出餐')
    
    print(f"自动回复线程池: {reply_status}")
    print(f"自动回评线程池: {comment_status}")
    print(f"自动出餐线程池: {meal_status}")
    
    # 测试繁忙状态检查
    print(f"\n自动回复线程池是否繁忙: {is_executor_busy(reply_executor)}")
    print(f"自动回评线程池是否繁忙: {is_executor_busy(comment_executor)}")
    print(f"自动出餐线程池是否繁忙: {is_executor_busy(meal_executor)}")

def test_task_progress():
    """测试任务进度跟踪"""
    print("\n=== 任务进度测试 ===")
    
    with task_progress_lock:
        print(f"当前任务进度:")
        print(f"  自动回复: {task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']}")
        print(f"  自动回评: {task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']}")
        print(f"  自动出餐: {task_progress['auto_meal']['completed']}/{task_progress['auto_meal']['total']}")

def simulate_task_dispatch():
    """模拟任务分发逻辑"""
    print("\n=== 模拟任务分发 ===")
    
    # 检查各个线程池是否可以分发新任务
    can_dispatch_reply = not is_executor_busy(reply_executor)
    can_dispatch_comment = not is_executor_busy(comment_executor)
    can_dispatch_meal = not is_executor_busy(meal_executor)
    
    print(f"可以分发自动回复任务: {can_dispatch_reply}")
    print(f"可以分发自动回评任务: {can_dispatch_comment}")
    print(f"可以分发自动出餐任务: {can_dispatch_meal}")
    
    # 模拟同时分发的情况
    if can_dispatch_reply and can_dispatch_comment and can_dispatch_meal:
        print("✅ 三种任务可以同时分发")
    else:
        print("⚠️ 部分任务需要等待:")
        if not can_dispatch_reply:
            print("  - 自动回复线程池繁忙")
        if not can_dispatch_comment:
            print("  - 自动回评线程池繁忙")
        if not can_dispatch_meal:
            print("  - 自动出餐线程池繁忙")

def main():
    """主测试函数"""
    print("🚀 开始测试优化后的调度器逻辑")
    print("=" * 50)
    
    test_executor_status()
    test_task_progress()
    simulate_task_dispatch()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")
    
    # 打印优化要点
    print("\n📋 优化要点总结:")
    print("1. 三种任务可以同时分发，不再有时间间隔限制")
    print("2. 每种任务独立检查线程池状态")
    print("3. 任务进度独立跟踪和重置")
    print("4. 增加了详细的线程池状态监控")
    print("5. 通过任务进度可以观察一次scheduler_loop的执行效果")

if __name__ == "__main__":
    main()
