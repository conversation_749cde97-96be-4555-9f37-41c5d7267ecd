#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证清理 REPLY_DISPATCH_INTERVAL 后的代码
确保所有功能正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试模块导入...")
    
    try:
        from routes.task_routes import (
            reply_executor,
            comment_executor, 
            meal_executor,
            COMMENT_DISPATCH_INTERVAL,
            task_progress,
            task_progress_lock,
            is_executor_busy,
            get_executor_status
        )
        print("✅ 所有必要模块导入成功")
        
        # 验证配置
        print(f"📋 当前配置:")
        print(f"  自动回复线程池: {reply_executor._max_workers} 个工作线程")
        print(f"  自动回评线程池: {comment_executor._max_workers} 个工作线程")
        print(f"  自动出餐线程池: {meal_executor._max_workers} 个工作线程")
        print(f"  自动回评分发间隔: {COMMENT_DISPATCH_INTERVAL} 秒 ({COMMENT_DISPATCH_INTERVAL//60} 分钟)")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_removed_variables():
    """测试已删除的变量确实不存在"""
    print(f"\n🗑️ 验证已删除的变量...")
    
    try:
        from routes.task_routes import REPLY_DISPATCH_INTERVAL
        print(f"❌ REPLY_DISPATCH_INTERVAL 仍然存在，应该已被删除")
        return False
    except ImportError:
        print(f"✅ REPLY_DISPATCH_INTERVAL 已成功删除")
    
    try:
        from routes.task_routes import shop_last_reply_time
        print(f"❌ shop_last_reply_time 仍然存在，应该已被删除")
        return False
    except ImportError:
        print(f"✅ shop_last_reply_time 已成功删除")
    
    try:
        from routes.task_routes import shop_last_reply_time_lock
        print(f"❌ shop_last_reply_time_lock 仍然存在，应该已被删除")
        return False
    except ImportError:
        print(f"✅ shop_last_reply_time_lock 已成功删除")
    
    return True

def test_thread_pool_status():
    """测试线程池状态检查功能"""
    print(f"\n🔧 测试线程池状态检查...")
    
    try:
        from routes.task_routes import is_executor_busy, get_executor_status, reply_executor, comment_executor, meal_executor
        
        # 测试线程池状态
        reply_busy = is_executor_busy(reply_executor)
        comment_busy = is_executor_busy(comment_executor)
        meal_busy = is_executor_busy(meal_executor)
        
        print(f"  自动回复线程池繁忙: {reply_busy}")
        print(f"  自动回评线程池繁忙: {comment_busy}")
        print(f"  自动出餐线程池繁忙: {meal_busy}")
        
        # 测试详细状态
        reply_status = get_executor_status(reply_executor, "自动回复")
        comment_status = get_executor_status(comment_executor, "自动回评")
        meal_status = get_executor_status(meal_executor, "自动出餐")
        
        print(f"  自动回复详细状态: {reply_status}")
        print(f"  自动回评详细状态: {comment_status}")
        print(f"  自动出餐详细状态: {meal_status}")
        
        print(f"✅ 线程池状态检查功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 线程池状态检查失败: {e}")
        return False

def test_progress_tracking():
    """测试进度跟踪功能"""
    print(f"\n📊 测试进度跟踪功能...")
    
    try:
        from routes.task_routes import task_progress, task_progress_lock
        
        with task_progress_lock:
            # 显示当前进度
            print(f"  当前任务进度:")
            print(f"    自动回复: {task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']}")
            print(f"    自动回评: {task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']}")
            print(f"    自动出餐: {task_progress['auto_meal']['completed']}/{task_progress['auto_meal']['total']}")
            
            # 测试进度更新
            original_reply_total = task_progress['auto_reply']['total']
            task_progress['auto_reply']['total'] = 100
            task_progress['auto_reply']['completed'] = 50
            
            print(f"  测试进度更新:")
            print(f"    自动回复: {task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']}")
            
            # 恢复原值
            task_progress['auto_reply']['total'] = original_reply_total
            task_progress['auto_reply']['completed'] = 0
        
        print(f"✅ 进度跟踪功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 进度跟踪测试失败: {e}")
        return False

def test_comment_interval_logic():
    """测试自动回评时间间隔逻辑"""
    print(f"\n⏰ 测试自动回评时间间隔逻辑...")
    
    try:
        from routes.task_routes import COMMENT_DISPATCH_INTERVAL
        import time
        
        # 模拟时间检查逻辑
        current_time = time.time()
        last_comment_dispatch_time = 0  # 从未分发过
        
        # 检查是否可以分发
        time_since_last = current_time - last_comment_dispatch_time
        can_dispatch = time_since_last >= COMMENT_DISPATCH_INTERVAL
        
        print(f"  距离上次分发: {int(time_since_last)} 秒")
        print(f"  需要间隔: {COMMENT_DISPATCH_INTERVAL} 秒")
        print(f"  可以分发: {'✅ 是' if can_dispatch else '❌ 否'}")
        
        # 模拟分发后的状态
        if can_dispatch:
            last_comment_dispatch_time = current_time
            print(f"  📤 模拟分发成功，下次分发: {COMMENT_DISPATCH_INTERVAL//60}分钟后")
            
            # 立即再次检查（应该被拒绝）
            time_since_last = current_time - last_comment_dispatch_time
            can_dispatch_again = time_since_last >= COMMENT_DISPATCH_INTERVAL
            
            if not can_dispatch_again:
                remaining_time = COMMENT_DISPATCH_INTERVAL - time_since_last
                remaining_minutes = int(remaining_time / 60)
                print(f"  ⏰ 立即再次分发被拒绝，还需等待 {remaining_minutes} 分钟")
        
        print(f"✅ 自动回评时间间隔逻辑正常")
        return True
        
    except Exception as e:
        print(f"❌ 自动回评时间间隔测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 验证清理 REPLY_DISPATCH_INTERVAL 后的代码")
    print("=" * 60)
    
    all_tests_passed = True
    
    # 1. 测试导入
    if not test_imports():
        all_tests_passed = False
    
    # 2. 验证已删除的变量
    if not test_removed_variables():
        all_tests_passed = False
    
    # 3. 测试线程池状态
    if not test_thread_pool_status():
        all_tests_passed = False
    
    # 4. 测试进度跟踪
    if not test_progress_tracking():
        all_tests_passed = False
    
    # 5. 测试自动回评时间间隔
    if not test_comment_interval_logic():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    
    if all_tests_passed:
        print("✅ 所有测试通过！代码清理成功")
        
        print(f"\n📋 清理总结:")
        print(f"1. ✅ 删除了 REPLY_DISPATCH_INTERVAL 变量")
        print(f"2. ✅ 删除了 shop_last_reply_time 相关代码")
        print(f"3. ✅ 删除了 shop_last_reply_time_lock 锁")
        print(f"4. ✅ 简化了 reply_worker_batch 函数")
        print(f"5. ✅ 简化了 scheduler_loop 函数")
        print(f"6. ✅ 保留了自动回评的时间间隔控制")
        print(f"7. ✅ 保留了精细化进度跟踪功能")
        print(f"8. ✅ 保留了线程池状态检查功能")
        
        print(f"\n💡 优势:")
        print(f"  - 代码更简洁，减少了不必要的复杂性")
        print(f"  - 避免了重复的频率控制逻辑")
        print(f"  - 依赖底层脚本的店铺级别控制")
        print(f"  - 保持了带宽节省的自动回评间隔控制")
        
    else:
        print("❌ 部分测试失败，请检查代码")

if __name__ == "__main__":
    main()
