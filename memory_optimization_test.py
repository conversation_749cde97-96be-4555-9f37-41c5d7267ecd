import requests
import json
import time


def get_shop_info(cookies):
    """获取店铺信息"""
    try:
        # 处理cookie输入（支持字符串或字典列表）
        if isinstance(cookies, str):
            # 如果是字符串，直接使用
            cookie_str = cookies
            # 解析为字典
            cookie_dict = {}
            for part in cookies.split(';'):
                part = part.strip()
                if '=' in part:
                    k, v = part.split('=', 1)
                    cookie_dict[k.strip()] = v.strip()
        else:
            # 如果是字典列表，转换为字符串
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
            cookie_str = "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
        
        # 获取acctId和region信息
        acct_id = cookie_dict.get('acctId', '')
        region_id = cookie_dict.get('region_id', '1000420600')
        region_version = cookie_dict.get('region_version', '1743386677')
        
        if not acct_id:
            print("错误：cookie中缺少acctId")
            return None
        
        # 请求店铺信息
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Cookie": cookie_str,
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Referer": "https://e.waimai.meituan.com/new_fe/orderbusiness",
            "Origin": "https://e.waimai.meituan.com"
        }
        
        params = {
            "region_id": region_id,
            "region_version": region_version,
            "acctId": acct_id,
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "3.2.1",
            "mtgsig": json.dumps({
                "a1": "1.2",
                "a2": int(time.time() * 1000),
                "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
                "a5": "XOE8Teh8fT2R0q/348/l89lo9fV2PMCykjNws7Z3n6qEBz3VFmeXpyw3ZCYG5uoWTc==",
                "a6": "hs1.6gX5QvP3kL9C+7jPyUu16Kjp5iynl4ZirdlkeL38JQK2jEpjVIqzJi4uMYccDDdqOldT5zqPpXc207Gcbu6dv0CZhvpvq4BJ25W4au53UnFb/yMkA70AjrH9cD0yPKqLUzobP8m9OB9FKtr/g6uagmg==",
                "a8": "f0364663eac0f26bc68b492e2e1eda21",
                "a9": "3.2.1,7,69",
                "a10": "2c",
                "x0": 4,
                "d1": "979a24e3631aafd931ae0b53f4808b38"
            })
        }
        
        url = "https://e.waimai.meituan.com/api/v2/account/homePage"
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        
        result = response.json()
        
        if result.get('code') == 0:
            data = result.get('data', {})
            return {
                'acctName': data.get('acctName', ''),
                'wmPoiName': data.get('wmPoiName', ''),
                'wmPoiId': data.get('wmPoiId', ''),
                'regionId': data.get('regionId', ''),
                'regionVersion': data.get('regionVersion', '')
            }
        else:
            print(f"获取店铺信息失败: {result.get('msg', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"获取店铺信息出错: {e}")
        return None

if __name__ == "__main__":
    # 示例cookie字符串
    example_cookie = "_lxsdk_s=19863d60f32-3f8-e3d-189%7C%7C10; pushToken=0-4rbJGOhKmtrV4vPpMfeVm6LApxTdyJXFemJ6lwZCDo*; set_info=%7B%22wmPoiId%22%3A%2224260310%22%2C%22region_id%22%3A%************%22%2C%22region_version%22%3A1723274333%7D; set_info_single=%7B%22regionIdForSingle%22%3A%************%22%2C%22regionVersionForSingle%22%3A1723274333%7D; provinceId=410000; token=0-4rbJGOhKmtrV4vPpMfeVm6LApxTdyJXFemJ6lwZCDo*; utm_source_rg=; logan_session_token=84flsojztv8ih7ij9nuo; region_version=1723274333; onlyForDaoDianAcct=0; _lxsdk=19862f8f3dfc8-03138fb583d66c8-26031d51-4b9600-19862f8f3dfc8; region_id=1000331000; ignore_set_router_proxy=false; isChain=0; scIndex=0; acctId=205788023; city_id=331082; has_not_waimai_poi=0; device_uuid=!e0323acd-0f68-4cb9-97f6-43cfd2290632; location_id=331082; bsid=oFdqcam0LbN1AXpfng_adQ-Yhxqkg6QhlUDXU4lGEGcY16UCk3qfUes7j4OMr0LyrTAqNoNXVabsKzSJu0LZ4w; isOfflineSelfOpen=0; shopCategory=food; wmPoiId=24260310; JSESSIONID=g1fjhczw5tx9drhnvhf3vot; uuid_update=true; wpush_server_url=wss://wpush.meituan.com; city_location_id=331000; WEBDFPID=0v9221062z105y56z6w217281x1w4y5u80137u1w51v979584107vv93-1754093539673-1754007135870MSMQQUYfd79fef3d01d5e9aadc18ccd4d0c95072776; cityId=410900; _lxsdk_cuid=19862f8f3dfc8-03138fb583d66c8-26031d51-4b9600-19862f8f3dfc8"
    
    # 示例调用
    info = get_shop_info(example_cookie)
    print(info)