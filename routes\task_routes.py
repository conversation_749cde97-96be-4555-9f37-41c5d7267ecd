#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务管理路由
"""

import threading
import time
from flask import Blueprint, jsonify
from utils.db_utils import get_connection
import queue
import uuid
import random
from concurrent.futures import ThreadPoolExecutor, as_completed

task_bp = Blueprint('task', __name__)

# 定时任务线程
scheduler_thread = None
scheduler_running = False

# 三个任务队列和线程
reply_queue = queue.Queue()
comment_queue = queue.Queue()
meal_queue = queue.Queue()
reply_thread = None
comment_thread = None
meal_thread = None

# 线程池全局声明 - 极度优化网络带宽使用（适配低带宽环境）
reply_executor = ThreadPoolExecutor(max_workers=5)  # 自动回复
comment_executor = ThreadPoolExecutor(max_workers=1)  # 自动回评
meal_executor = ThreadPoolExecutor(max_workers=10)  # 自动出餐

# 自动回复分发节流控制
last_reply_dispatch_time = 0
REPLY_DISPATCH_INTERVAL = 5  # 秒，店铺级别的回复间隔控制

# 自动回评分发时间间隔控制
last_comment_dispatch_time = 0
COMMENT_DISPATCH_INTERVAL = 3600  # 秒，自动回评分发间隔（默认1小时）

# 任务分发控制 - 移除时间间隔限制，改为纯线程池状态控制
# COMMENT_DISPATCH_INTERVAL = 3600  # 移除回评时间间隔限制
# MEAL_DISPATCH_INTERVAL = 5  # 移除出餐时间间隔限制
# last_comment_dispatch_time = 0  # 不再需要
# last_meal_dispatch_time = 0  # 不再需要
# 线程安全锁
import threading

def is_executor_busy(executor):
    """检查线程池是否繁忙（是否还有空闲线程）"""
    try:
        # 检查线程池的队列大小和活跃线程数
        if hasattr(executor, '_threads'):
            active_threads = len(executor._threads)
            max_workers = executor._max_workers
            # 检查是否有排队的任务
            queue_size = executor._work_queue.qsize() if hasattr(executor, '_work_queue') else 0
            # 如果活跃线程数达到最大值或有排队任务，说明线程池繁忙
            return active_threads >= max_workers or queue_size > 0
        return False
    except:
        return False

def get_executor_status(executor, name):
    """获取线程池详细状态信息"""
    try:
        active_threads = len(executor._threads) if hasattr(executor, '_threads') else 0
        max_workers = executor._max_workers
        queue_size = executor._work_queue.qsize() if hasattr(executor, '_work_queue') else 0
        is_busy = active_threads >= max_workers or queue_size > 0

        return {
            'name': name,
            'active_threads': active_threads,
            'max_workers': max_workers,
            'queue_size': queue_size,
            'is_busy': is_busy,
            'status': '繁忙' if is_busy else '空闲'
        }
    except Exception as e:
        return {
            'name': name,
            'active_threads': 0,
            'max_workers': 0,
            'queue_size': 0,
            'is_busy': False,
            'status': '未知',
            'error': str(e)
        }
# 记录每个店铺最后执行自动回复的时间
shop_last_reply_time = {}
shop_last_reply_time_lock = threading.Lock()
# 统计信息
task_stats = {
    'auto_reply': {'success': 0, 'skip': 0, 'error': 0},
    'auto_comment': {'success': 0, 'error': 0},
    'auto_meal': {'success': 0, 'error': 0}
}
last_stats_print_time = 0

# 任务进度跟踪
task_progress = {
    'auto_reply': {'total': 0, 'completed': 0},
    'auto_comment': {'total': 0, 'completed': 0},
    'auto_meal': {'total': 0, 'completed': 0}
}
task_progress_lock = threading.Lock()

# 内存清理相关
last_memory_cleanup_time = 0


def cleanup_memory():
    """定期清理内存，防止内存泄漏"""
    global last_memory_cleanup_time
    current_time = time.time()

    # 每30分钟执行一次内存清理
    if current_time - last_memory_cleanup_time >= 1800:  # 30分钟
        cleaned_items = 0

        # 清理过期的店铺回复时间记录
        with shop_last_reply_time_lock:
            expired_shops = []
            for shop_id, last_time in shop_last_reply_time.items():
                if current_time - last_time > 7200:  # 2小时
                    expired_shops.append(shop_id)
            for shop_id in expired_shops:
                del shop_last_reply_time[shop_id]
            cleaned_items += len(expired_shops)

        # 不再需要清理executing_shops，改用线程池状态检查

        # 强制垃圾回收
        import gc
        collected = gc.collect()

        if cleaned_items > 0 or collected > 0:
            print(f"[内存清理] 清理了 {cleaned_items} 个过期记录，回收了 {collected} 个对象")

        last_memory_cleanup_time = current_time


# 自动回复并发worker
def reply_worker_batch(tasks):
    for task in tasks:
        shop_id = task['shop_id']
        current_time = time.time()

        # 检查该店铺距离上次执行是否已超过间隔时间
        with shop_last_reply_time_lock:
            last_time = shop_last_reply_time.get(shop_id, 0)
            if current_time - last_time < REPLY_DISPATCH_INTERVAL:
                task_stats['auto_reply']['skip'] += 1
                continue
            # 更新执行时间
            shop_last_reply_time[shop_id] = current_time

        try:
            from meituan_auto_message_sender import auto_send_messages_to_unreplied, parse_cookies
            cookies = parse_cookies(task['cookie'])

            # 记录开始时间
            start_time = time.time()

            # 执行自动回复
            success_count = auto_send_messages_to_unreplied(
                cookies=cookies,
                sender_uid=task['sender_uid'],
                device_id=task['device_id'],
                message_text=task['message_texts']
            )

            # 记录执行结果 - 只有当成功回复数量大于0时才记录日志
            if success_count > 0:
                task_stats['auto_reply']['success'] += 1
                end_time = time.time()
                execution_time = end_time - start_time

                # 记录日志到数据库
                try:
                    connection = get_connection()
                    cursor = connection.cursor()
                    from datetime import datetime
                    now = datetime.now()

                    # 清理3天前的日志
                    cursor.execute("DELETE FROM shop_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 DAY)")

                    # 插入日志记录
                    log_message = f"自动回复任务执行完成，成功回复 {success_count} 个聊天，耗时: {execution_time:.2f}秒"
                    cursor.execute(
                        "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                        [task['shop_id'], task['shop_name'], 'auto_reply', log_message, now]
                    )
                    connection.commit()
                    cursor.close()
                    connection.close()
                except Exception as e:
                    print(f"[自动回复] 记录日志失败: {e}")

        except Exception as e:
            task_stats['auto_reply']['error'] += 1
            print(f"[自动回复] 店铺{task['shop_name']}任务异常: {e}")
            # 记录错误日志
            try:
                connection = get_connection()
                cursor = connection.cursor()
                from datetime import datetime
                now = datetime.now()

                log_message = f"自动回复任务执行失败: {str(e)}"
                cursor.execute(
                    "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                    [task['shop_id'], task['shop_name'], 'auto_reply', log_message, now]
                )
                connection.commit()
                cursor.close()
                connection.close()
            except Exception as log_e:
                print(f"[自动回复] 记录错误日志失败: {log_e}")
        finally:
            # 更新任务进度
            with task_progress_lock:
                task_progress['auto_reply']['completed'] += 1


# 自动回评并发worker
def comment_worker_batch(tasks):
    for task in tasks:
        try:
            from meituan_auto_reply import auto_reply_comments, parse_cookies
            cookies = parse_cookies(task['cookie'])

            # 记录开始时间
            start_time = time.time()

            # 执行自动回评
            success_count = auto_reply_comments(
                cookies=cookies,
                good_texts=task['good_texts'],
                mid_texts=task['mid_texts'],
                bad_texts=task['bad_texts']
            )

            # 记录执行结果 - 只有当成功回复数量大于0时才记录日志
            if success_count > 0:
                task_stats['auto_comment']['success'] += 1
                end_time = time.time()
                execution_time = end_time - start_time

                # 记录日志到数据库
                try:
                    connection = get_connection()
                    cursor = connection.cursor()
                    from datetime import datetime
                    now = datetime.now()

                    # 清理3天前的日志
                    cursor.execute("DELETE FROM shop_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 DAY)")

                    # 插入日志记录
                    log_message = f"自动回评任务执行完成，成功回复 {success_count} 条评论，耗时: {execution_time:.2f}秒"
                    cursor.execute(
                        "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                        [task['shop_id'], task['shop_name'], 'auto_comment', log_message, now]
                    )
                    connection.commit()
                    cursor.close()
                    connection.close()
                except Exception as e:
                    print(f"[自动回评] 记录日志失败: {e}")

        except Exception as e:
            task_stats['auto_comment']['error'] += 1
            print(f"[自动回评] 店铺{task['shop_name']}任务异常: {e}")
            # 记录错误日志
            try:
                connection = get_connection()
                cursor = connection.cursor()
                from datetime import datetime
                now = datetime.now()

                log_message = f"自动回评任务执行失败: {str(e)}"
                cursor.execute(
                    "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                    [task['shop_id'], task['shop_name'], 'auto_comment', log_message, now]
                )
                connection.commit()
                cursor.close()
                connection.close()
            except Exception as log_e:
                print(f"[自动回评] 记录错误日志失败: {log_e}")

        # 更新任务进度
        with task_progress_lock:
            task_progress['auto_comment']['completed'] += 1


# 自动出餐并发worker
def meal_worker_batch(tasks):
    for task in tasks:
        try:
            from meituan_unprocessed_orders import auto_complete_meal, parse_cookies
            cookies = parse_cookies(task['cookie'])

            # 记录开始时间
            start_time = time.time()

            # 执行自动出餐
            success_count, completed_orders = auto_complete_meal(
                cookies=cookies,
                meal_duration=task['meal_duration']
            )

            # 记录执行结果 - 只有当成功出餐数量大于0时才记录日志
            if success_count > 0:
                task_stats['auto_meal']['success'] += 1
                end_time = time.time()
                execution_time = end_time - start_time

                # 构建详细的订单信息日志
                order_details = []
                for i, order in enumerate(completed_orders, 1):
                    detail = (f"订单{i}:\n"
                              f"  订单号: {order['order_id']}\n"
                              f"  日序号: {order.get('wm_poi_order_dayseq', 'N/A')}\n"
                              f"  订单类型: {order.get('order_type', '普通订单')}\n"
                              f"  下单时间: {order['order_time']}\n"
                              f"  出餐时间: {order['complete_time']}\n"
                              f"  出餐用时: {order['meal_time_used']}秒")
                    if order.get('delivery_time'):
                        detail += f"\n  配送时间: {order['delivery_time']}"
                    order_details.append(detail)

                # 记录日志到数据库
                try:
                    connection = get_connection()
                    cursor = connection.cursor()
                    from datetime import datetime
                    now = datetime.now()

                    # 清理3天前的日志
                    cursor.execute("DELETE FROM shop_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 DAY)")

                    # 构建日志消息
                    log_message = f"自动出餐任务执行完成，成功出餐 {success_count} 个订单，耗时: {execution_time:.2f}秒"
                    if order_details:
                        log_message += f"\n\n订单详情:\n" + "\n".join(order_details)

                    cursor.execute(
                        "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                        [task['shop_id'], task['shop_name'], 'auto_meal', log_message, now]
                    )
                    connection.commit()
                    cursor.close()
                    connection.close()
                except Exception as e:
                    print(f"[自动出餐] 记录日志失败: {e}")

        except Exception as e:
            task_stats['auto_meal']['error'] += 1
            print(f"[自动出餐] ❌ 店铺 {task['shop_name']} (ID: {task['shop_id']}) 获取未出餐订单失败: {e}")
            print(f"[自动出餐] 失败详情 - 店铺: {task['shop_name']}, 错误类型: {type(e).__name__}, 错误信息: {str(e)}")
            # 记录错误日志
            try:
                connection = get_connection()
                cursor = connection.cursor()
                from datetime import datetime
                now = datetime.now()

                log_message = f"自动出餐任务执行失败: {str(e)}"
                cursor.execute(
                    "INSERT INTO shop_logs (shop_id, shop_name, task_type, log_message, created_at) VALUES (%s, %s, %s, %s, %s)",
                    [task['shop_id'], task['shop_name'], 'auto_meal', log_message, now]
                )
                connection.commit()
                cursor.close()
                connection.close()
            except Exception as log_e:
                print(f"[自动出餐] 记录错误日志失败: {log_e}")

        # 更新任务进度
        with task_progress_lock:
            task_progress['auto_meal']['completed'] += 1


# --- 新增：定时检测cookie有效性线程 ---

def check_shop_info_static(cookies):
    """静态方法：检查cookie有效性，不创建浏览器对象"""
    try:
        import requests
        import json
        import time

        # 处理cookie输入（支持字符串或字典列表）
        if isinstance(cookies, str):
            # 如果是字符串，直接使用
            cookie_str = cookies
            # 解析为字典
            cookie_dict = {}
            for part in cookies.split(';'):
                part = part.strip()
                if '=' in part:
                    k, v = part.split('=', 1)
                    cookie_dict[k.strip()] = v.strip()
        else:
            # 如果是字典列表，转换为字符串
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
            cookie_str = "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])

        # 获取acctId和region信息
        acct_id = cookie_dict.get('acctId', '')
        region_id = cookie_dict.get('region_id', '1000420600')
        region_version = cookie_dict.get('region_version', '1743386677')

        if not acct_id:
            # print("错误：cookie中缺少acctId")  # 减少打印
            return None

        # 请求店铺信息
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Cookie": cookie_str,
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Referer": "https://e.waimai.meituan.com/new_fe/orderbusiness",
            "Origin": "https://e.waimai.meituan.com"
        }

        params = {
            "region_id": region_id,
            "region_version": region_version,
            "acctId": acct_id,
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "3.2.1",
            "mtgsig": json.dumps({
                "a1": "1.2",
                "a2": int(time.time() * 1000),
                "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
                "a5": "XOE8Teh8fT2R0q/348/l89lo9fV2PMCykjNws7Z3n6qEBz3VFmeXpyw3ZCYG5uoWTc==",
                "a6": "hs1.6gX5QvP3kL9C+7jPyUu16Kjp5iynl4ZirdlkeL38JQK2jEpjVIqzJi4uMYccDDdqOldT5zqPpXc207Gcbu6dv0CZhvpvq4BJ25W4au53UnFb/yMkA70AjrH9cD0yPKqLUzobP8m9OB9FKtr/g6uagmg==",
                "a8": "f0364663eac0f26bc68b492e2e1eda21",
                "a9": "3.2.1,7,69",
                "a10": "2c",
                "x0": 4,
                "d1": "979a24e3631aafd931ae0b53f4808b38"
            })
        }

        url = "https://e.waimai.meituan.com/api/v2/account/homePage"
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()

        result = response.json()

        if result.get('code') == 0:
            data = result.get('data', {})
            return {
                'acctName': data.get('acctName', ''),
                'wmPoiName': data.get('wmPoiName', ''),
                'wmPoiId': data.get('wmPoiId', ''),
                'regionId': data.get('regionId', ''),
                'regionVersion': data.get('regionVersion', '')
            }
        else:
            # print(f"获取店铺信息失败: {result.get('msg', '未知错误')}")  # 减少打印
            return None

    except Exception as e:
        # print(f"检查cookie有效性出错: {e}")  # 减少打印
        return None
def check_cookie_worker():
    while True:
        try:
            connection = get_connection()
            cursor = connection.cursor(dictionary=True)
            from datetime import datetime
            now = datetime.now()
            # 查询所有未过期的店铺
            cursor.execute("SELECT id, cookie FROM shops WHERE expire_time IS NULL OR expire_time > %s", [now])
            shops = cursor.fetchall()
            # 直接使用HTTP请求检查cookie有效性，不需要创建浏览器对象
            for shop in shops:
                try:
                    # 解析cookie为list[dict]
                    cookies = []
                    for part in shop['cookie'].split(';'):
                        part = part.strip()
                        if not part: continue
                        if '=' in part:
                            k, v = part.split('=', 1)
                            cookies.append({'name': k.strip(), 'value': v.strip()})

                    # 直接调用静态方法检查cookie，避免创建对象
                    info = check_shop_info_static(cookies)
                    ck_status = 1 if info else 0
                    cursor.execute("UPDATE shops SET ck_status = %s WHERE id = %s", [ck_status, shop['id']])
                except Exception as e:
                    print(f"检测店铺{shop['id']} cookie异常: {e}")  # 减少打印
                    cursor.execute("UPDATE shops SET ck_status = 0 WHERE id = %s", [shop['id']])
            connection.commit()
            cursor.close()
            connection.close()
        except Exception as e:
            print(f"[Cookie检测] 任务异常: {e}")
        time.sleep(600)  # 1小时


# 主调度线程

def print_task_stats():
    """每5分钟打印一次任务统计信息"""
    global task_stats, last_stats_print_time
    current_time = time.time()

    # 每5分钟打印一次统计信息
    if current_time - last_stats_print_time >= 300:  # 300秒 = 5分钟
        # 清理过期的店铺回复时间记录（超过1小时的记录）
        with shop_last_reply_time_lock:
            expired_shops = []
            for shop_id, last_time in shop_last_reply_time.items():
                if current_time - last_time > 3600:  # 1小时
                    expired_shops.append(shop_id)
            for shop_id in expired_shops:
                del shop_last_reply_time[shop_id]
            if expired_shops:
                print(f"[内存清理] 清理了 {len(expired_shops)} 个过期的店铺回复时间记录")

        # 不再需要清理executing_shops

        # 添加线程池状态监控
        reply_active = reply_executor._threads.__len__() if hasattr(reply_executor, '_threads') else 0
        comment_active = comment_executor._threads.__len__() if hasattr(comment_executor, '_threads') else 0
        meal_active = meal_executor._threads.__len__() if hasattr(meal_executor, '_threads') else 0

        # 检查排队任务数量
        reply_queue_size = reply_executor._work_queue.qsize() if hasattr(reply_executor, '_work_queue') else 0
        comment_queue_size = comment_executor._work_queue.qsize() if hasattr(comment_executor, '_work_queue') else 0
        meal_queue_size = meal_executor._work_queue.qsize() if hasattr(meal_executor, '_work_queue') else 0

        print(
            f"[任务统计] 自动回复: 成功{task_stats['auto_reply']['success']}, 跳过{task_stats['auto_reply']['skip']}, 错误{task_stats['auto_reply']['error']} (活跃线程:{reply_active}/{reply_executor._max_workers}, 排队:{reply_queue_size})")
        print(
            f"[任务统计] 自动回评: 成功{task_stats['auto_comment']['success']}, 错误{task_stats['auto_comment']['error']} (活跃线程:{comment_active}/{comment_executor._max_workers}, 排队:{comment_queue_size})")
        print(
            f"[任务统计] 自动出餐: 成功{task_stats['auto_meal']['success']}, 错误{task_stats['auto_meal']['error']} (活跃线程:{meal_active}/{meal_executor._max_workers}, 排队:{meal_queue_size})")
        print(f"[内存状态] 店铺回复时间记录数: {len(shop_last_reply_time)}")

        # 添加内存使用监控
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            print(f"[内存监控] 当前进程内存使用: {memory_mb:.1f}MB")
        except ImportError:
            pass  # 如果没有psutil模块就跳过

        # 重置统计计数器
        task_stats = {
            'auto_reply': {'success': 0, 'skip': 0, 'error': 0},
            'auto_comment': {'success': 0, 'error': 0},
            'auto_meal': {'success': 0, 'error': 0}
        }
        last_stats_print_time = current_time


def print_task_progress():
    """打印当前任务执行进度"""
    with task_progress_lock:
        reply_progress = f"{task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']}"
        comment_progress = f"{task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']}"
        meal_progress = f"{task_progress['auto_meal']['completed']}/{task_progress['auto_meal']['total']}"

        # 只有当有任务时才打印进度
        if (task_progress['auto_reply']['total'] > 0 or
            task_progress['auto_comment']['total'] > 0 or
            task_progress['auto_meal']['total'] > 0):
            print(f"[任务进度] 自动回复: {reply_progress}, 自动回评: {comment_progress}, 自动出餐: {meal_progress}")

            # 检查是否所有任务都完成，如果完成则重置计数器
            all_reply_done = task_progress['auto_reply']['completed'] >= task_progress['auto_reply']['total'] and task_progress['auto_reply']['total'] > 0
            all_comment_done = task_progress['auto_comment']['completed'] >= task_progress['auto_comment']['total'] and task_progress['auto_comment']['total'] > 0
            all_meal_done = task_progress['auto_meal']['completed'] >= task_progress['auto_meal']['total'] and task_progress['auto_meal']['total'] > 0

            # 如果某个任务类型完成了，重置其计数器
            if all_reply_done:
                print(f"✅ 自动回复任务全部完成: {task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']}")
                task_progress['auto_reply']['total'] = 0
                task_progress['auto_reply']['completed'] = 0
            if all_comment_done:
                print(f"✅ 自动回评任务全部完成: {task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']}")
                task_progress['auto_comment']['total'] = 0
                task_progress['auto_comment']['completed'] = 0
            if all_meal_done:
                print(f"✅ 自动出餐任务全部完成: {task_progress['auto_meal']['completed']}/{task_progress['auto_meal']['total']}")
                task_progress['auto_meal']['total'] = 0
                task_progress['auto_meal']['completed'] = 0


def scheduler_loop(interval=15):  # 调度间隔，用于观察任务执行情况
    global scheduler_running, last_reply_dispatch_time, last_comment_dispatch_time
    while scheduler_running:
        try:
            # 定期清理内存
            cleanup_memory()

            # 检查各个任务类型是否可以开始新一轮任务（独立检查）
            with task_progress_lock:
                # 自动回复：如果线程池空闲且当前批次已完成，标记可以开始新批次
                reply_can_start_new_batch = (not is_executor_busy(reply_executor) and
                    task_progress['auto_reply']['completed'] >= task_progress['auto_reply']['total'] and
                    task_progress['auto_reply']['total'] > 0)

                # 自动回评：如果线程池空闲且当前批次已完成，标记可以开始新批次
                comment_can_start_new_batch = (not is_executor_busy(comment_executor) and
                    task_progress['auto_comment']['completed'] >= task_progress['auto_comment']['total'] and
                    task_progress['auto_comment']['total'] > 0)

                # 自动出餐：如果线程池空闲且当前批次已完成，标记可以开始新批次
                meal_can_start_new_batch = (not is_executor_busy(meal_executor) and
                    task_progress['auto_meal']['completed'] >= task_progress['auto_meal']['total'] and
                    task_progress['auto_meal']['total'] > 0)

                # 如果某个任务类型可以开始新批次，重置其计数器
                if reply_can_start_new_batch:
                    print(f"🔄 自动回复批次完成，准备开始新批次: {task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']}")
                    task_progress['auto_reply']['total'] = 0
                    task_progress['auto_reply']['completed'] = 0

                if comment_can_start_new_batch:
                    print(f"🔄 自动回评批次完成，准备开始新批次: {task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']}")
                    task_progress['auto_comment']['total'] = 0
                    task_progress['auto_comment']['completed'] = 0

                if meal_can_start_new_batch:
                    print(f"🔄 自动出餐批次完成，准备开始新批次: {task_progress['auto_meal']['completed']}/{task_progress['auto_meal']['total']}")
                    task_progress['auto_meal']['total'] = 0
                    task_progress['auto_meal']['completed'] = 0

            # 打印统计信息和任务进度
            print_task_stats()
            print_task_progress()

            connection = get_connection()
            cursor = connection.cursor(dictionary=True)
            from datetime import datetime
            now = datetime.now()
            # 1. 查所有需要的店铺 - 修改查询条件，排除 expire_time 为 null 的店铺
            query = '''
            SELECT id, shop_name, cookie, sender_uid, device_id, expire_time, ck_status,
                   auto_reply, reply_message_text,
                   auto_comment, auto_comment_types, comment_good_text, comment_mid_text, comment_bad_text,
                   auto_meal, auto_meal_periods
            FROM shops
            WHERE ck_status = 1
              AND expire_time IS NOT NULL
              AND expire_time > %s
              AND (auto_reply = 1 OR auto_comment = 1 OR auto_meal = 1)
            '''
            cursor.execute(query, [now])
            shops = cursor.fetchall()
            # 2. 查所有系统消息
            cursor.execute("SELECT * FROM system_messages WHERE is_active=1")
            all_sys_msgs = cursor.fetchall()
            # 3. 组装参数，分发任务（三种任务可以同时分发）
            import time as pytime
            now_ts = pytime.time()

            # === 自动回复任务分发 ===
            # 检查是否可以分发新的自动回复任务
            reply_can_dispatch = False
            with task_progress_lock:
                # 情况1：没有正在执行的任务（total=0）
                # 情况2：线程池空闲且当前批次已完成
                if (task_progress['auto_reply']['total'] == 0 or
                    (not is_executor_busy(reply_executor) and
                     task_progress['auto_reply']['completed'] >= task_progress['auto_reply']['total'])):
                    reply_can_dispatch = True

            if reply_can_dispatch:
                reply_tasks = []
                for shop in shops:
                    if shop['auto_reply']:
                        shop_id = shop['id']

                        # 检查该店铺距离上次执行时间是否足够（店铺级别的节流控制）
                        with shop_last_reply_time_lock:
                            last_time = shop_last_reply_time.get(shop_id, 0)
                            if now_ts - last_time < REPLY_DISPATCH_INTERVAL:
                                continue

                        message_texts = []
                        if shop.get('reply_message_text'):
                            message_texts = [t.strip() for t in shop['reply_message_text'].replace('\r', '').split('\n')
                                             if t.strip()]
                            if not message_texts:
                                message_texts = [shop['reply_message_text']]
                        if not message_texts:
                            message_texts = [msg['message_text'] for msg in all_sys_msgs if
                                             msg['message_type'] == 'auto_reply']
                        reply_tasks.append({
                            'shop_id': shop['id'],
                            'shop_name': shop['shop_name'],
                            'cookie': shop['cookie'],
                            'sender_uid': shop['sender_uid'],
                            'device_id': shop['device_id'],
                            'message_texts': message_texts
                        })

                # 分发自动回复任务
                if reply_tasks:
                    with task_progress_lock:
                        # 如果是新批次，重置计数器
                        if task_progress['auto_reply']['total'] == 0:
                            task_progress['auto_reply']['completed'] = 0
                        task_progress['auto_reply']['total'] = len(reply_tasks)
                    max_workers = reply_executor._max_workers
                    batch_size = max(1, (len(reply_tasks) + max_workers - 1) // max_workers)
                    for i in range(max_workers):
                        batch = reply_tasks[i * batch_size:(i + 1) * batch_size]
                        if batch:
                            reply_executor.submit(reply_worker_batch, batch)
                            time.sleep(0.1)
                    last_reply_dispatch_time = now_ts
                    print(f"📤 分发自动回复任务: {len(reply_tasks)} 个店铺")
                else:
                    print(f"📭 自动回复无可分发任务")
            else:
                with task_progress_lock:
                    current_progress = f"{task_progress['auto_reply']['completed']}/{task_progress['auto_reply']['total']}"
                print(f"⏳ 自动回复任务进行中，跳过分发 (进度: {current_progress})")

            # === 自动回评任务分发 ===
            # 检查是否可以分发新的自动回评任务
            comment_can_dispatch = False
            with task_progress_lock:
                # 情况1：没有正在执行的任务（total=0）
                # 情况2：线程池空闲且当前批次已完成
                if (task_progress['auto_comment']['total'] == 0 or
                    (not is_executor_busy(comment_executor) and
                     task_progress['auto_comment']['completed'] >= task_progress['auto_comment']['total'])):
                    # 额外检查时间间隔限制
                    if now_ts - last_comment_dispatch_time >= COMMENT_DISPATCH_INTERVAL:
                        comment_can_dispatch = True
                    else:
                        remaining_time = COMMENT_DISPATCH_INTERVAL - (now_ts - last_comment_dispatch_time)
                        remaining_minutes = int(remaining_time / 60)
                        print(f"⏰ 自动回评距离下次分发还需等待 {remaining_minutes} 分钟")

            if comment_can_dispatch:
                comment_tasks = []
                for shop in shops:
                    if shop['auto_comment']:
                        types = shop.get('auto_comment_types', 'good,mid,bad').split(',')
                        good_texts, mid_texts, bad_texts = [], [], []
                        if 'good' in types and shop.get('comment_good_text'):
                            good_texts = [t.strip() for t in shop['comment_good_text'].replace('\r', '').split('\n') if
                                          t.strip()]
                        if 'mid' in types and shop.get('comment_mid_text'):
                            mid_texts = [t.strip() for t in shop['comment_mid_text'].replace('\r', '').split('\n') if
                                         t.strip()]
                        if 'bad' in types and shop.get('comment_bad_text'):
                            bad_texts = [t.strip() for t in shop['comment_bad_text'].replace('\r', '').split('\n') if
                                         t.strip()]
                        if 'good' in types and not good_texts:
                            good_texts = [msg['message_text'] for msg in all_sys_msgs if
                                          msg['message_type'] == 'auto_comment_good']
                        if 'mid' in types and not mid_texts:
                            mid_texts = [msg['message_text'] for msg in all_sys_msgs if
                                         msg['message_type'] == 'auto_comment_mid']
                        if 'bad' in types and not bad_texts:
                            bad_texts = [msg['message_text'] for msg in all_sys_msgs if
                                         msg['message_type'] == 'auto_comment_bad']
                        comment_tasks.append({
                            'shop_id': shop['id'],
                            'shop_name': shop['shop_name'],
                            'cookie': shop['cookie'],
                            'good_texts': good_texts if 'good' in types else None,
                            'mid_texts': mid_texts if 'mid' in types else None,
                            'bad_texts': bad_texts if 'bad' in types else None
                        })

                # 分发自动回评任务
                if comment_tasks:
                    with task_progress_lock:
                        # 如果是新批次，重置计数器
                        if task_progress['auto_comment']['total'] == 0:
                            task_progress['auto_comment']['completed'] = 0
                        task_progress['auto_comment']['total'] = len(comment_tasks)
                    max_workers = comment_executor._max_workers
                    batch_size = max(1, (len(comment_tasks) + max_workers - 1) // max_workers)
                    for i in range(max_workers):
                        batch = comment_tasks[i * batch_size:(i + 1) * batch_size]
                        if batch:
                            comment_executor.submit(comment_worker_batch, batch)
                            time.sleep(0.1)
                    # 更新分发时间戳
                    last_comment_dispatch_time = now_ts
                    print(f"📤 分发自动回评任务: {len(comment_tasks)} 个店铺 (下次分发: {COMMENT_DISPATCH_INTERVAL//60}分钟后)")
                else:
                    print(f"📭 自动回评无可分发任务")
            else:
                with task_progress_lock:
                    current_progress = f"{task_progress['auto_comment']['completed']}/{task_progress['auto_comment']['total']}"
                print(f"⏳ 自动回评任务进行中，跳过分发 (进度: {current_progress})")

            # === 自动出餐任务分发 ===
            # 检查是否可以分发新的自动出餐任务
            meal_can_dispatch = False
            with task_progress_lock:
                # 情况1：没有正在执行的任务（total=0）
                # 情况2：线程池空闲且当前批次已完成
                if (task_progress['auto_meal']['total'] == 0 or
                    (not is_executor_busy(meal_executor) and
                     task_progress['auto_meal']['completed'] >= task_progress['auto_meal']['total'])):
                    meal_can_dispatch = True

            if meal_can_dispatch:
                meal_tasks = []
                for shop in shops:
                    if shop['auto_meal']:
                        import json
                        from datetime import datetime
                        meal_periods = []
                        if shop.get('auto_meal_periods'):
                            try:
                                meal_periods = json.loads(shop['auto_meal_periods'])
                            except Exception:
                                pass

                        def get_current_period_duration(periods):
                            now = datetime.now()
                            now_minutes = now.hour * 60 + now.minute

                            # 找到"全天"标签的默认duration
                            default_duration = 300
                            for period in periods:
                                if period.get('label') == '全天':
                                    default_duration = period.get('duration', 300)
                                    break

                            # 检查是否在非"全天"的自定义时间段内
                            for period in periods:
                                try:
                                    # 跳过"全天"标签，只处理自定义时间段
                                    if period.get('label') == '全天':
                                        continue

                                    start_h, start_m = map(int, period['start'].split(':'))
                                    end_h, end_m = map(int, period['end'].split(':'))
                                    start_minutes = start_h * 60 + start_m
                                    end_minutes = end_h * 60 + end_m

                                    # 跨天处理
                                    if start_minutes <= end_minutes:
                                        in_period = start_minutes <= now_minutes <= end_minutes
                                    else:
                                        in_period = now_minutes >= start_minutes or now_minutes <= end_minutes

                                    if in_period:
                                        # 在自定义时间段内，使用该时间段的duration
                                        return period.get('duration', default_duration)
                                except Exception:
                                    continue

                            # 不在任何自定义时间段内，使用"全天"的默认duration
                            return default_duration

                        meal_duration = get_current_period_duration(meal_periods)
                        meal_tasks.append({
                            'shop_id': shop['id'],
                            'shop_name': shop['shop_name'],
                            'cookie': shop['cookie'],
                            'meal_duration': meal_duration
                        })

                # 分发自动出餐任务
                if meal_tasks:
                    with task_progress_lock:
                        # 如果是新批次，重置计数器
                        if task_progress['auto_meal']['total'] == 0:
                            task_progress['auto_meal']['completed'] = 0
                        task_progress['auto_meal']['total'] = len(meal_tasks)
                    max_workers = meal_executor._max_workers
                    batch_size = max(1, (len(meal_tasks) + max_workers - 1) // max_workers)
                    for i in range(max_workers):
                        batch = meal_tasks[i * batch_size:(i + 1) * batch_size]
                        if batch:
                            meal_executor.submit(meal_worker_batch, batch)
                            time.sleep(0.1)
                    print(f"📤 分发自动出餐任务: {len(meal_tasks)} 个店铺")
                else:
                    print(f"📭 自动出餐无可分发任务")
            else:
                with task_progress_lock:
                    current_progress = f"{task_progress['auto_meal']['completed']}/{task_progress['auto_meal']['total']}"
                print(f"⏳ 自动出餐任务进行中，跳过分发 (进度: {current_progress})")
            cursor.close()
            connection.close()
        except Exception as e:
            print(f"[调度器] 定时任务执行异常: {e}")
            # 确保异常情况下也关闭数据库连接
            try:
                if 'cursor' in locals():
                    cursor.close()
                if 'connection' in locals():
                    connection.close()
            except:
                pass
        time.sleep(interval)


# --- 自动启动定时任务线程 ---
if not scheduler_running:
    scheduler_running = True
    scheduler_thread = threading.Thread(target=scheduler_loop, daemon=True)
    scheduler_thread.start()
    # 自动回复现在使用线程池，不需要单独的worker线程
    # 不要再启动 comment_worker_batch 和 meal_worker_batch 的线程
    # 启动cookie检测线程
    cookie_check_thread = threading.Thread(target=check_cookie_worker, daemon=True)
    cookie_check_thread.start()


@task_bp.route('/start-scheduler', methods=['POST'])
def start_scheduler():
    """启动定时任务"""
    global scheduler_thread, scheduler_running
    if scheduler_running:
        return jsonify({'code': 1, 'message': '定时任务已在运行'})
    scheduler_running = True
    scheduler_thread = threading.Thread(target=scheduler_loop, daemon=True)
    scheduler_thread.start()
    return jsonify({'code': 0, 'message': '定时任务已启动'})


@task_bp.route('/stop-scheduler', methods=['POST'])
def stop_scheduler():
    """停止定时任务"""
    global scheduler_running
    scheduler_running = False
    return jsonify({'code': 0, 'message': '定时任务已停止'})