(['D:\\dm\\py\\测试\\waimai\\meituan_gui_login_modern.py'],
 ['D:\\dm\\py\\测试\\waimai'],
 ['customtkinter',
  'requests',
  'json',
  'pickle',
  'threading',
  'tkinterselenium',
  'webdriver_manager'],
 [('D:\\python\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\python\\lib\\site-packages\\pypinyin\\__pyinstaller', 0),
  ('D:\\python\\lib\\site-packages\\rapidfuzz\\__pyinstaller', 0),
  ('D:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\python\\lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('drivers\\chromedriver.exe',
   'D:\\dm\\py\\测试\\waimai\\drivers\\chromedriver.exe',
   'DATA'),
  ('meituan_login_cdp.py',
   'D:\\dm\\py\\测试\\waimai\\meituan_login_cdp.py',
   'DATA')],
 '3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'D:\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'D:\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('meituan_gui_login_modern',
   'D:\\dm\\py\\测试\\waimai\\meituan_gui_login_modern.py',
   'PYSOURCE')],
 [('setuptools',
   'D:\\python\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.six',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\six.py',
   'PYMODULE'),
  ('struct', 'D:\\python\\lib\\struct.py', 'PYMODULE'),
  ('__future__', 'D:\\python\\lib\\__future__.py', 'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid', 'D:\\python\\lib\\uuid.py', 'PYMODULE'),
  ('hashlib', 'D:\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\python\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('_strptime', 'D:\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'D:\\python\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\python\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('gettext', 'D:\\python\\lib\\gettext.py', 'PYMODULE'),
  ('random', 'D:\\python\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\python\\lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\python\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\python\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\python\\lib\\numbers.py', 'PYMODULE'),
  ('bisect', 'D:\\python\\lib\\bisect.py', 'PYMODULE'),
  ('socket', 'D:\\python\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\python\\lib\\selectors.py', 'PYMODULE'),
  ('subprocess', 'D:\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('contextlib', 'D:\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('signal', 'D:\\python\\lib\\signal.py', 'PYMODULE'),
  ('shutil', 'D:\\python\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'D:\\python\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._common', 'D:\\python\\lib\\importlib\\_common.py', 'PYMODULE'),
  ('importlib._adapters',
   'D:\\python\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('typing', 'D:\\python\\lib\\typing.py', 'PYMODULE'),
  ('tempfile', 'D:\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\python\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\python\\lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\python\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\python\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\python\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\python\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\python\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\python\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\python\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\python\\lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\python\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\python\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'D:\\python\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\python\\lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\python\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\python\\lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\python\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\python\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\python\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase', 'D:\\python\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.header', 'D:\\python\\lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\python\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\python\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\python\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\python\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('quopri', 'D:\\python\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\python\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\python\\lib\\optparse.py', 'PYMODULE'),
  ('email', 'D:\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\python\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\python\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'D:\\python\\lib\\csv.py', 'PYMODULE'),
  ('importlib.readers', 'D:\\python\\lib\\importlib\\readers.py', 'PYMODULE'),
  ('tokenize', 'D:\\python\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\python\\lib\\token.py', 'PYMODULE'),
  ('pathlib', 'D:\\python\\lib\\pathlib.py', 'PYMODULE'),
  ('importlib.util', 'D:\\python\\lib\\importlib\\util.py', 'PYMODULE'),
  ('tarfile', 'D:\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\python\\lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\python\\lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'D:\\python\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\python\\lib\\bz2.py', 'PYMODULE'),
  ('platform', 'D:\\python\\lib\\platform.py', 'PYMODULE'),
  ('pdb', 'D:\\python\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server', 'D:\\python\\lib\\http\\server.py', 'PYMODULE'),
  ('http', 'D:\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\python\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('urllib.request', 'D:\\python\\lib\\urllib\\request.py', 'PYMODULE'),
  ('getpass', 'D:\\python\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\python\\lib\\netrc.py', 'PYMODULE'),
  ('ssl', 'D:\\python\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'D:\\python\\lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'D:\\python\\lib\\urllib\\error.py', 'PYMODULE'),
  ('socketserver', 'D:\\python\\lib\\socketserver.py', 'PYMODULE'),
  ('mimetypes', 'D:\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.client', 'D:\\python\\lib\\http\\client.py', 'PYMODULE'),
  ('html', 'D:\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\python\\lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\python\\lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\python\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'D:\\python\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'D:\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'D:\\python\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess', 'D:\\python\\lib\\_bootsubprocess.py', 'PYMODULE'),
  ('pkgutil', 'D:\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\python\\lib\\zipimport.py', 'PYMODULE'),
  ('runpy', 'D:\\python\\lib\\runpy.py', 'PYMODULE'),
  ('shlex', 'D:\\python\\lib\\shlex.py', 'PYMODULE'),
  ('inspect', 'D:\\python\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'D:\\python\\lib\\ast.py', 'PYMODULE'),
  ('glob', 'D:\\python\\lib\\glob.py', 'PYMODULE'),
  ('code', 'D:\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\python\\lib\\codeop.py', 'PYMODULE'),
  ('dis', 'D:\\python\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\python\\lib\\opcode.py', 'PYMODULE'),
  ('bdb', 'D:\\python\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\python\\lib\\cmd.py', 'PYMODULE'),
  ('pprint', 'D:\\python\\lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('copy', 'D:\\python\\lib\\copy.py', 'PYMODULE'),
  ('string', 'D:\\python\\lib\\string.py', 'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('ctypes', 'D:\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\python\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('imp', 'D:\\python\\lib\\imp.py', 'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('html.parser', 'D:\\python\\lib\\html\\parser.py', 'PYMODULE'),
  ('_markupbase', 'D:\\python\\lib\\_markupbase.py', 'PYMODULE'),
  ('setuptools.extern',
   'D:\\python\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\python\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\python\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\python\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.log', 'D:\\python\\lib\\distutils\\log.py', 'PYMODULE'),
  ('distutils', 'D:\\python\\lib\\distutils\\__init__.py', 'PYMODULE'),
  ('distutils.archive_util',
   'D:\\python\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util', 'D:\\python\\lib\\distutils\\dir_util.py', 'PYMODULE'),
  ('distutils.file_util',
   'D:\\python\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util', 'D:\\python\\lib\\distutils\\dep_util.py', 'PYMODULE'),
  ('distutils.spawn', 'D:\\python\\lib\\distutils\\spawn.py', 'PYMODULE'),
  ('distutils.debug', 'D:\\python\\lib\\distutils\\debug.py', 'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\python\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support', 'D:\\python\\lib\\_osx_support.py', 'PYMODULE'),
  ('distutils.text_file',
   'D:\\python\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\python\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\python\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\python\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\python\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\python\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('site', 'D:\\python\\lib\\site.py', 'PYMODULE'),
  ('rlcompleter', 'D:\\python\\lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'D:\\python\\lib\\_sitebuiltins.py', 'PYMODULE'),
  ('distutils.extension',
   'D:\\python\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\python\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\python\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.py27compat',
   'D:\\python\\lib\\site-packages\\setuptools\\py27compat.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\python\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'D:\\python\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.py33compat',
   'D:\\python\\lib\\site-packages\\setuptools\\py33compat.py',
   'PYMODULE'),
  ('distutils.version', 'D:\\python\\lib\\distutils\\version.py', 'PYMODULE'),
  ('setuptools.dist',
   'D:\\python\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\python\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.py31compat',
   'D:\\python\\lib\\site-packages\\setuptools\\py31compat.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\python\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\python\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\python\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\python\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\python\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\python\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.install_scripts',
   'D:\\python\\lib\\site-packages\\setuptools\\command\\install_scripts.py',
   'PYMODULE'),
  ('distutils.command.install_scripts',
   'D:\\python\\lib\\distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\python\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\python\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\python\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'D:\\python\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\python\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.easy_install',
   'D:\\python\\lib\\site-packages\\setuptools\\command\\easy_install.py',
   'PYMODULE'),
  ('pkg_resources.py31compat',
   'D:\\python\\lib\\site-packages\\pkg_resources\\py31compat.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\python\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.package_index',
   'D:\\python\\lib\\site-packages\\setuptools\\package_index.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\python\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.sandbox',
   'D:\\python\\lib\\site-packages\\setuptools\\sandbox.py',
   'PYMODULE'),
  ('distutils.command.build_scripts',
   'D:\\python\\lib\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('distutils.command.install',
   'D:\\python\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('configparser', 'D:\\python\\lib\\configparser.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\python\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\python\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\python\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\python\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\python\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\python\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\python\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\python\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\python\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\python\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\python\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\python\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\python\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\python\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\python\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\python\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'D:\\python\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.six',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\six.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'D:\\python\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.shell',
   'D:\\python\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com',
   'D:\\python\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'D:\\python\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'D:\\python\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'D:\\python\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'D:\\python\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'D:\\python\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'D:\\python\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'D:\\python\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.client',
   'D:\\python\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'D:\\python\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'D:\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'D:\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'D:\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'D:\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'D:\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'D:\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'D:\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'D:\\python\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'D:\\python\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'D:\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'D:\\python\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'D:\\python\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'D:\\python\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'D:\\python\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'D:\\python\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.build',
   'D:\\python\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('pywintypes',
   'D:\\python\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('win32com.server',
   'D:\\python\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('winerror',
   'D:\\python\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.universal',
   'D:\\python\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.util',
   'D:\\python\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('pythoncom', 'D:\\python\\lib\\site-packages\\pythoncom.py', 'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\python\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('plistlib', 'D:\\python\\lib\\plistlib.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\python\\lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.parsers', 'D:\\python\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'D:\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\python\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax', 'D:\\python\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'D:\\python\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\python\\lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('setuptools.config',
   'D:\\python\\lib\\site-packages\\setuptools\\config.py',
   'PYMODULE'),
  ('distutils.dist', 'D:\\python\\lib\\distutils\\dist.py', 'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\python\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.cmd', 'D:\\python\\lib\\distutils\\cmd.py', 'PYMODULE'),
  ('setuptools.ssl_support',
   'D:\\python\\lib\\site-packages\\setuptools\\ssl_support.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\python\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\python\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\python\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'D:\\python\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\python\\lib\\fnmatch.py', 'PYMODULE'),
  ('distutils.util', 'D:\\python\\lib\\distutils\\util.py', 'PYMODULE'),
  ('distutils.errors', 'D:\\python\\lib\\distutils\\errors.py', 'PYMODULE'),
  ('distutils.filelist', 'D:\\python\\lib\\distutils\\filelist.py', 'PYMODULE'),
  ('distutils.core', 'D:\\python\\lib\\distutils\\core.py', 'PYMODULE'),
  ('distutils.config', 'D:\\python\\lib\\distutils\\config.py', 'PYMODULE'),
  ('cgi', 'D:\\python\\lib\\cgi.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\python\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\python\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('hmac', 'D:\\python\\lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\python\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\python\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager',
   'D:\\python\\lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('stringprep', 'D:\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('meituan_login_cdp',
   'D:\\dm\\py\\测试\\waimai\\meituan_login_cdp.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\python\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\python\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\python\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('idna', 'D:\\python\\lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.package_data',
   'D:\\python\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\python\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core', 'D:\\python\\lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.uts46data',
   'D:\\python\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\python\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\python\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\python\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\python\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\python\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\python\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\python\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\python\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\python\\lib\\ipaddress.py', 'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\python\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   'D:\\python\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\python\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\python\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\python\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines', 'D:\\python\\lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\python\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants', 'D:\\python\\lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio', 'D:\\python\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'D:\\python\\lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.subprocess', 'D:\\python\\lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\python\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\python\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\python\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock', 'D:\\python\\lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\python\\lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\python\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\python\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.base_tasks', 'D:\\python\\lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\python\\lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'D:\\python\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports', 'D:\\python\\lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.protocols', 'D:\\python\\lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.futures', 'D:\\python\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.exceptions', 'D:\\python\\lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.events', 'D:\\python\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('urllib3.util',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\python\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\python\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\python\\lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\python\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\python\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\python\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\python\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\python\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\python\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\python\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\python\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\python\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\python\\lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\python\\lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE'),
  ('brotli', 'D:\\python\\lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('urllib3.response',
   'D:\\python\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\python\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\python\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\python\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\python\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\python\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\python\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\python\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\python\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\python\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\python\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.types',
   'D:\\python\\lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\edge\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chrome\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.websocket_connection',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\websocket_connection.py',
   'PYMODULE'),
  ('selenium.common',
   'D:\\python\\lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('websocket',
   'D:\\python\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._socket',
   'D:\\python\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._utils',
   'D:\\python\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'D:\\python\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'D:\\python\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._core',
   'D:\\python\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._http',
   'D:\\python\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._url',
   'D:\\python\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._handshake',
   'D:\\python\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'D:\\python\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('http.cookies', 'D:\\python\\lib\\http\\cookies.py', 'PYMODULE'),
  ('websocket._app',
   'D:\\python\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._logging',
   'D:\\python\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._abnf',
   'D:\\python\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\python\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks', 'D:\\python\\lib\\site-packages\\socks.py', 'PYMODULE'),
  ('certifi',
   'D:\\python\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\python\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.locator_converter',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\locator_converter.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.fedcm',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\fedcm.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.client_config',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\client_config.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.dialog',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\dialog.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.account',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\account.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.webextension',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\webextension.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.common',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\common.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.storage',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\storage.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.session',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\session.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.script',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\script.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.log',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\log.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.permissions',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\permissions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.network',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\network.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browsing_context',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browsing_context.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browser',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browser.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'D:\\python\\lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'D:\\python\\lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\python\\lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\python\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\python\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\python\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\python\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'D:\\python\\lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom', 'D:\\python\\lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium',
   'D:\\python\\lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('queue', 'D:\\python\\lib\\queue.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'D:\\python\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('tkinter.messagebox', 'D:\\python\\lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\python\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter', 'D:\\python\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants', 'D:\\python\\lib\\tkinter\\constants.py', 'PYMODULE'),
  ('customtkinter',
   'D:\\python\\lib\\site-packages\\customtkinter\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_tk',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\ctk_tk.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility.utility_functions',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\utility\\utility_functions.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\utility\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\image\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image.ctk_image',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\image\\ctk_image.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\python\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\python\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.Image', 'D:\\python\\lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\python\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\python\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\python\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\python\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\python\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\python\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\python\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\python\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'D:\\python\\lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\python\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\python\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\python\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\python\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('numpy', 'D:\\python\\lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\python\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\python\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\python\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\python\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\python\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\python\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\python\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\python\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\python\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\python\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\python\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\python\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\python\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\python\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\python\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\python\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\python\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\python\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil', 'D:\\python\\lib\\site-packages\\psutil\\__init__.py', 'PYMODULE'),
  ('psutil._pswindows',
   'D:\\python\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\python\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('doctest', 'D:\\python\\lib\\doctest.py', 'PYMODULE'),
  ('difflib', 'D:\\python\\lib\\difflib.py', 'PYMODULE'),
  ('unittest.case', 'D:\\python\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'D:\\python\\lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.util', 'D:\\python\\lib\\unittest\\util.py', 'PYMODULE'),
  ('unittest.result', 'D:\\python\\lib\\unittest\\result.py', 'PYMODULE'),
  ('numpy.testing._private',
   'D:\\python\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest', 'D:\\python\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\python\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'D:\\python\\lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'D:\\python\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\python\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\python\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\python\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\python\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\python\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\python\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\python\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\python\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\python\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\python\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\python\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\python\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\python\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\python\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\python\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\python\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\python\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\python\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\python\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\python\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\python\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\python\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\python\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\python\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\python\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\python\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\python\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\python\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\python\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\python\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\python\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\python\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('threadpoolctl',
   'D:\\python\\lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'D:\\python\\lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('ctypes.util', 'D:\\python\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\python\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\python\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\python\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\python\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\python\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\python\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\python\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\python\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\python\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\python\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\python\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('yaml', 'D:\\python\\lib\\site-packages\\yaml\\__init__.py', 'PYMODULE'),
  ('yaml.cyaml', 'D:\\python\\lib\\site-packages\\yaml\\cyaml.py', 'PYMODULE'),
  ('yaml.resolver',
   'D:\\python\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\python\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\python\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\python\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\python\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\python\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\python\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\python\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\python\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\python\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\python\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes', 'D:\\python\\lib\\site-packages\\yaml\\nodes.py', 'PYMODULE'),
  ('yaml.events',
   'D:\\python\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\python\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error', 'D:\\python\\lib\\site-packages\\yaml\\error.py', 'PYMODULE'),
  ('numpy.array_api',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\python\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\python\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\python\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\python\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\python\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\python\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\python\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\python\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\python\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\python\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\python\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\python\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\python\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\python\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('cffi', 'D:\\python\\lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi.error', 'D:\\python\\lib\\site-packages\\cffi\\error.py', 'PYMODULE'),
  ('cffi.api', 'D:\\python\\lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.recompiler',
   'D:\\python\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\python\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\python\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\python\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock', 'D:\\python\\lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\python\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\python\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\python\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\python\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\python\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\python\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\python\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\python\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\python\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\python\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\python\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\python\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\python\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\python\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\python\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\python\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\python\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\python\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model', 'D:\\python\\lib\\site-packages\\cffi\\model.py', 'PYMODULE'),
  ('PIL._util', 'D:\\python\\lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._binary',
   'D:\\python\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\python\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\python\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\python\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL', 'D:\\python\\lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL._version',
   'D:\\python\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollable_frame',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollable_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_textbox',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_textbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_tabview',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_tabview.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_switch',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_switch.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_slider',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_slider.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_segmented_button',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_segmented_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollbar',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_radiobutton',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_radiobutton.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_progressbar',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_progressbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_optionmenu',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_optionmenu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_label',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_label.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_frame',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_entry',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_entry.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_combobox',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_combobox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_checkbox',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_checkbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_button',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\ctk_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.ctk_base_class',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\ctk_base_class.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\python\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.dropdown_menu',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\dropdown_menu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.draw_engine',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\draw_engine.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.ctk_canvas',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\ctk_canvas.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\theme\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme.theme_manager',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\theme\\theme_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_tracker',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_base_class',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\font\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.font_manager',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\font\\font_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.ctk_font',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\font\\ctk_font.py',
   'PYMODULE'),
  ('tkinter.font', 'D:\\python\\lib\\tkinter\\font.py', 'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_tracker',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_tracker.py',
   'PYMODULE'),
  ('darkdetect',
   'D:\\python\\lib\\site-packages\\darkdetect\\__init__.py',
   'PYMODULE'),
  ('darkdetect._linux_detect',
   'D:\\python\\lib\\site-packages\\darkdetect\\_linux_detect.py',
   'PYMODULE'),
  ('darkdetect._windows_detect',
   'D:\\python\\lib\\site-packages\\darkdetect\\_windows_detect.py',
   'PYMODULE'),
  ('darkdetect._dummy',
   'D:\\python\\lib\\site-packages\\darkdetect\\_dummy.py',
   'PYMODULE'),
  ('darkdetect._mac_detect',
   'D:\\python\\lib\\site-packages\\darkdetect\\_mac_detect.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_base_class',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_input_dialog',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\ctk_input_dialog.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_toplevel',
   'D:\\python\\lib\\site-packages\\customtkinter\\windows\\ctk_toplevel.py',
   'PYMODULE'),
  ('tkinter.filedialog', 'D:\\python\\lib\\tkinter\\filedialog.py', 'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\python\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.dialog', 'D:\\python\\lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('datetime', 'D:\\python\\lib\\datetime.py', 'PYMODULE'),
  ('pickle', 'D:\\python\\lib\\pickle.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\python\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('threading', 'D:\\python\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\python\\lib\\_threading_local.py', 'PYMODULE'),
  ('requests',
   'D:\\python\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\python\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\python\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\python\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\python\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\python\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\python\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\python\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\python\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\python\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\python\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\python\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\python\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\python\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\python\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\python\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\python\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('json', 'D:\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\python\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\python\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\python\\lib\\json\\scanner.py', 'PYMODULE')],
 [('drivers\\chromedriver.exe',
   'D:\\dm\\py\\测试\\waimai\\drivers\\chromedriver.exe',
   'BINARY'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python310.dll', 'D:\\python\\python310.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'D:\\python\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom310.dll',
   'D:\\python\\lib\\site-packages\\pywin32_system32\\pythoncom310.dll',
   'BINARY'),
  ('_hashlib.pyd', 'D:\\python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'D:\\python\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\python\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'D:\\python\\lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'D:\\python\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'D:\\python\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'D:\\python\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\python\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'D:\\python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_cffi_backend.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\_cffi_backend.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\zstandard\\_cffi.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\zstandard\\backend_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_brotli.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\_brotli.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'D:\\python\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\python\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\python\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\yaml\\_yaml.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'D:\\python\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\python\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll', 'D:\\python\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'D:\\python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'D:\\python\\DLLs\\libffi-7.dll', 'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'D:\\python\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll', 'D:\\python\\python3.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\python\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\python\\DLLs\\tcl86t.dll', 'BINARY'),
  ('numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'D:\\python\\lib\\site-packages\\numpy.libs\\libopenblas64__v0.3.23-293-gc2f4bdbb-gcc_10_3_0-2bde3a66a51006b2b53eb373ff767a3f.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\JAVA\\jre\\bin\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\JAVA\\jre\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY')],
 [],
 [],
 [('meituan_login_cdp.py',
   'D:\\dm\\py\\测试\\waimai\\meituan_login_cdp.py',
   'DATA'),
  ('base_library.zip',
   'D:\\dm\\py\\测试\\waimai\\build\\meituan_gui_login_modern\\base_library.zip',
   'DATA'),
  ('cryptography-44.0.2.dist-info\\METADATA',
   'D:\\python\\lib\\site-packages\\cryptography-44.0.2.dist-info\\METADATA',
   'DATA'),
  ('cryptography-44.0.2.dist-info\\INSTALLER',
   'D:\\python\\lib\\site-packages\\cryptography-44.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-44.0.2.dist-info\\licenses\\LICENSE',
   'D:\\python\\lib\\site-packages\\cryptography-44.0.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-44.0.2.dist-info\\WHEEL',
   'D:\\python\\lib\\site-packages\\cryptography-44.0.2.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-44.0.2.dist-info\\RECORD',
   'D:\\python\\lib\\site-packages\\cryptography-44.0.2.dist-info\\RECORD',
   'DATA'),
  ('cryptography-44.0.2.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\python\\lib\\site-packages\\cryptography-44.0.2.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-44.0.2.dist-info\\licenses\\LICENSE.BSD',
   'D:\\python\\lib\\site-packages\\cryptography-44.0.2.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\python\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\python\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\py.typed',
   'D:\\python\\lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'D:\\python\\lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\be.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\python\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\python\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\ko.msg', 'DATA'),
  ('_tk_data\\msgs\\it.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'D:\\python\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\sh.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tk_data\\tkfbox.tcl', 'D:\\python\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'D:\\python\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\python\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\hr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\EET', 'D:\\python\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\fa.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\python\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'D:\\python\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\init.tcl', 'D:\\python\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'D:\\python\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\kl.msg', 'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\kw.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tk_data\\entry.tcl', 'D:\\python\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\sl.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\th.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\hu.msg', 'DATA'),
  ('_tk_data\\choosedir.tcl', 'D:\\python\\tcl\\tk8.6\\choosedir.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\is.msg', 'DATA'),
  ('_tcl_data\\msgs\\fo.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\fo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\python\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\mt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0', 'D:\\python\\tcl\\tcl8.6\\tzdata\\GMT0', 'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'D:\\python\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\eu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST', 'D:\\python\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'D:\\python\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tk_data\\comdlg.tcl', 'D:\\python\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'D:\\python\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\package.tcl', 'D:\\python\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\sr.msg', 'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\gv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\tr.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\CET', 'D:\\python\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tk_data\\menu.tcl', 'D:\\python\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'D:\\python\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'D:\\python\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tk_data\\button.tcl', 'D:\\python\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tk_data\\msgs\\de.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\W-SU', 'D:\\python\\tcl\\tcl8.6\\tzdata\\W-SU', 'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\python\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\et.msg', 'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\history.tcl', 'D:\\python\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tk_data\\license.terms', 'D:\\python\\tcl\\tk8.6\\license.terms', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\te.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\GB', 'D:\\python\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\af.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'D:\\python\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tk_data\\console.tcl', 'D:\\python\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'D:\\python\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\ro.msg', 'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\python\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\python\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tk_data\\focus.tcl', 'D:\\python\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\python\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\hi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\HST', 'D:\\python\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\msgs\\pt.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\msgs\\da.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'D:\\python\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\WET', 'D:\\python\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\python\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tk_data\\msgs\\el.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\lt.msg', 'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tk_data\\palette.tcl', 'D:\\python\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tk_data\\images\\README',
   'D:\\python\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\sq.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\mr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran', 'D:\\python\\tcl\\tcl8.6\\tzdata\\Iran', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu', 'D:\\python\\tcl\\tcl8.6\\tzdata\\Zulu', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\nn.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'D:\\python\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\python\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\mk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tk_data\\bgerror.tcl', 'D:\\python\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\ar.msg', 'DATA'),
  ('_tcl_data\\msgs\\bn.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\bn.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\python\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'D:\\python\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'D:\\python\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tk_data\\tclIndex', 'D:\\python\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\sw.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Eire', 'D:\\python\\tcl\\tcl8.6\\tzdata\\Eire', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\ga.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tclIndex', 'D:\\python\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tk_data\\xmfbox.tcl', 'D:\\python\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'D:\\python\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'D:\\python\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\python\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tk_data\\scale.tcl', 'D:\\python\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tk_data\\text.tcl', 'D:\\python\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tk_data\\icons.tcl', 'D:\\python\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'D:\\python\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\ms.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\python\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\lv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\word.tcl', 'D:\\python\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'D:\\python\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\python\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tk_data\\listbox.tcl', 'D:\\python\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tk_data\\msgbox.tcl', 'D:\\python\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\python\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\uk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\bg.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\python\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba', 'D:\\python\\tcl\\tcl8.6\\tzdata\\Cuba', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\python\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'D:\\python\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\python\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\ca.msg', 'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tk_data\\iconlist.tcl', 'D:\\python\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tk_data\\msgs\\es.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'D:\\python\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\gl.msg', 'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\python\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'D:\\python\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tk_data\\msgs\\en.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\nb.msg', 'DATA'),
  ('_tcl_data\\msgs\\fi.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\msgs\\sk.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\sk.msg', 'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'D:\\python\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'D:\\python\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tcl_data\\msgs\\ru.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\msgs\\ta.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\ta.msg', 'DATA'),
  ('_tcl_data\\tm.tcl', 'D:\\python\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\zh.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\python\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\he.msg', 'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tk_data\\msgs\\da.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\ja.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'D:\\python\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'D:\\python\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tk_data\\obsolete.tcl', 'D:\\python\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\vi.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\python\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\python\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg', 'D:\\python\\tcl\\tcl8.6\\msgs\\id.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\python\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'D:\\python\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\python\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\python\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\python\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\python\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('customtkinter\\assets\\fonts\\Roboto\\Roboto-Regular.ttf',
   'D:\\python\\lib\\site-packages\\customtkinter\\assets\\fonts\\Roboto\\Roboto-Regular.ttf',
   'DATA'),
  ('customtkinter\\assets\\.DS_Store',
   'D:\\python\\lib\\site-packages\\customtkinter\\assets\\.DS_Store',
   'DATA'),
  ('customtkinter\\assets\\icons\\CustomTkinter_icon_Windows.ico',
   'D:\\python\\lib\\site-packages\\customtkinter\\assets\\icons\\CustomTkinter_icon_Windows.ico',
   'DATA'),
  ('customtkinter\\assets\\icons\\.DS_Store',
   'D:\\python\\lib\\site-packages\\customtkinter\\assets\\icons\\.DS_Store',
   'DATA'),
  ('customtkinter\\assets\\themes\\green.json',
   'D:\\python\\lib\\site-packages\\customtkinter\\assets\\themes\\green.json',
   'DATA'),
  ('customtkinter\\assets\\fonts\\Roboto\\Roboto-Medium.ttf',
   'D:\\python\\lib\\site-packages\\customtkinter\\assets\\fonts\\Roboto\\Roboto-Medium.ttf',
   'DATA'),
  ('customtkinter\\assets\\themes\\dark-blue.json',
   'D:\\python\\lib\\site-packages\\customtkinter\\assets\\themes\\dark-blue.json',
   'DATA'),
  ('customtkinter\\assets\\fonts\\CustomTkinter_shapes_font.otf',
   'D:\\python\\lib\\site-packages\\customtkinter\\assets\\fonts\\CustomTkinter_shapes_font.otf',
   'DATA'),
  ('customtkinter\\assets\\themes\\blue.json',
   'D:\\python\\lib\\site-packages\\customtkinter\\assets\\themes\\blue.json',
   'DATA'),
  ('pip-25.1.1.dist-info\\WHEEL',
   'D:\\python\\lib\\site-packages\\pip-25.1.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\LICENSE',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\LICENSE',
   'DATA'),
  ('wheel-0.45.1.dist-info\\INSTALLER',
   'D:\\python\\lib\\site-packages\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('pip-25.1.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\python\\lib\\site-packages\\pip-25.1.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\dependency_links.txt',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\dependency_links.txt',
   'DATA'),
  ('pip-25.1.1.dist-info\\top_level.txt',
   'D:\\python\\lib\\site-packages\\pip-25.1.1.dist-info\\top_level.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\REQUESTED',
   'D:\\python\\lib\\site-packages\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'D:\\python\\lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\zip-safe',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\zip-safe',
   'DATA'),
  ('pip-25.1.1.dist-info\\METADATA',
   'D:\\python\\lib\\site-packages\\pip-25.1.1.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'D:\\python\\lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\top_level.txt',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\top_level.txt',
   'DATA'),
  ('pip-25.1.1.dist-info\\RECORD',
   'D:\\python\\lib\\site-packages\\pip-25.1.1.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'D:\\python\\lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'D:\\python\\lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\METADATA',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\RECORD',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'D:\\python\\lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\REQUESTED',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('pip-25.1.1.dist-info\\entry_points.txt',
   'D:\\python\\lib\\site-packages\\pip-25.1.1.dist-info\\entry_points.txt',
   'DATA'),
  ('pip-25.1.1.dist-info\\INSTALLER',
   'D:\\python\\lib\\site-packages\\pip-25.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\entry_points.txt',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\entry_points.txt',
   'DATA'),
  ('pip-25.1.1.dist-info\\licenses\\AUTHORS.txt',
   'D:\\python\\lib\\site-packages\\pip-25.1.1.dist-info\\licenses\\AUTHORS.txt',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\INSTALLER',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-44.1.1.dist-info\\WHEEL',
   'D:\\python\\lib\\site-packages\\setuptools-44.1.1.dist-info\\WHEEL',
   'DATA'),
  ('pip-25.1.1.dist-info\\REQUESTED',
   'D:\\python\\lib\\site-packages\\pip-25.1.1.dist-info\\REQUESTED',
   'DATA')])
