import requests
import json
import urllib.parse
import time
from datetime import datetime

class MeituanUnprocessedOrders:
    def __init__(self, cookies=None):
        self.base_url = "https://e.waimai.meituan.com/gw/api/unified/r/order/list/page/unprocessed"
        self.preorder_setting_url = "https://e.waimai.meituan.com/gw/api/order/mix/preOrder/setting/query"
        self.headers = {
            "accept": "application/json, text/plain, */*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "content-type": "application/x-www-form-urlencoded",
            "origin": "https://e.waimai.meituan.com",
            "referer": "https://e.waimai.meituan.com/new_fe/orderbusiness",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
        }

        if cookies is None:
            self.cookies = {
                "_lxsdk_cuid": "19387f4eaeec8-04f196329ca6c6-4c657b58-144000-19387f4eaeec8",
                "token": "0pcBETpKIjVWYcYyH70DXfB9BqBMaAkvSBrKp5WeqUKs*",
                "wmPoiId": "14551304",
                "acctId": "126059518",
                "region_id": "1000440100",
                "region_version": "1651046402"
            }
        else:
            self.cookies = cookies

        self.headers["cookie"] = self._dict_to_cookie_string(self.cookies) if isinstance(self.cookies, dict) else self.cookies

        # 处理region_id和region_version的获取
        if isinstance(self.cookies, dict):
            self.region_id = self.cookies.get("region_id", "1000440100")
            self.region_version = self.cookies.get("region_version", "1651046402")
        else:
            # 如果是字符串cookie，需要解析获取region信息
            cookie_dict = self._parse_cookie_string(self.cookies)
            self.region_id = cookie_dict.get("region_id", "1000440100")
            self.region_version = cookie_dict.get("region_version", "1651046402")

        # 缓存预订单提醒时间，避免重复请求
        self._preorder_reminder_time = None

    def _dict_to_cookie_string(self, cookie_dict):
        return "; ".join([f"{key}={value}" for key, value in cookie_dict.items()])

    def _parse_cookie_string(self, cookie_str):
        """解析cookie字符串为字典"""
        cookie_dict = {}
        if not cookie_str:
            return cookie_dict

        for item in cookie_str.split(';'):
            if item.strip():
                try:
                    key, value = item.strip().split('=', 1)
                    cookie_dict[key.strip()] = value.strip()
                except ValueError:
                    pass
        return cookie_dict

    def get_preorder_reminder_time(self):
        """
        获取预订单提醒时间设置

        返回:
            int: 提醒时间（分钟），默认为60分钟
        """
        if self._preorder_reminder_time is not None:
            return self._preorder_reminder_time

        params = {
            "region_id": self.region_id,
            "region_version": self.region_version,
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "3.2.1",
            "mtgsig": json.dumps({
                "a1": "1.2",
                "a2": int(time.time() * 1000),
                "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
                "a5": "KQKbWdA8jKIUfKawix6MGvxsVv868xM8v8UJLxlUijo35IViPVbs",
                "a6": "hs1.60sgpXfbl4BIHitIoza/8tXGZggwytghOVEItMFwmyJpb5QS/M0altxFkRYE5GBNf2O7WNBa1O1hmNvlsJrDV5JcY2cJq9Ex3BmhUYu1EAejnaKM0MF3UnYpI/D3PsFRT",
                "a8": "83d026a0f4c7bfa3c00c426351d6f97b",
                "a9": "3.2.1,7,36",
                "a10": "5e",
                "x0": 4,
                "d1": "403543a485c23d0e3f07ca71c5f77ecf"
            })
        }

        try:
            response = requests.get(
                self.preorder_setting_url,
                params=params,
                headers=self.headers
            )
            response.raise_for_status()
            result = response.json()

            if result.get("code") == 0:
                data = result.get("data", {})
                self._preorder_reminder_time = data.get("preOrderReminderTime", 60)
                return self._preorder_reminder_time
            else:
                print(f"获取预订单设置失败: {result.get('msg', '未知错误')}")
                self._preorder_reminder_time = 60  # 默认60分钟
                return self._preorder_reminder_time

        except Exception as e:
            print(f"请求预订单设置失败: {e}")
            self._preorder_reminder_time = 60  # 默认60分钟
            return self._preorder_reminder_time

    def get_unprocessed_orders(self):
        params = {
            "region_id": self.region_id,
            "region_version": self.region_version,
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "3.2.1",
            "mtgsig": json.dumps({
                "a1": "1.2",
                "a2": int(time.time() * 1000),
                "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
                "a5": "m1Qntrc+d6j2fTYP980+37RB3W/AXme7CMAl6rfuViEn1nXVzph=",
                "a6": "hs1.6KJRMKVwlmQpXsZFFN3tOpIdOZpG6sLKebnzUFeRbdePrfy/dadG4Qcvf9/EilVijUS3I9932JGl0rYuNbGpAFRvqbGQE6ov0GdNNSLhQjSwvfOgEv2oBbkn9aUZ1fZVC",
                "a8": "068a37622c0d62ea972adf599a020eb9",
                "a9": "3.2.1,7,44",
                "a10": "9e",
                "x0": 4,
                "d1": "a01fa7fb06c33a0feb27c213a6dc9dfa"
            })
        }

        data = {
            "tag": "prepMeal",
            "pageParam": json.dumps({"pageSize": 10, "pageNum": 1, "sort": 1, "nextLabel": "", "lastLabel": ""}),
            "extParam": json.dumps({"phfRollback": 0})
        }

        try:
            response = requests.post(
                self.base_url,
                params=params,
                data=data,
                headers=self.headers
            )
            # print("响应状态码:", response.status_code)  # 减少打印
            response.raise_for_status()
            result = response.json()
            if result.get("code") != 0:
                print(result)
            return result
        except Exception as e:
            # print(f"请求失败: {e}")  # 减少打印
            return None

    def extract_order_data(self, order):
        common_info = json.loads(order.get("commonInfo", "{}"))
        delivery_btime = common_info.get("delivery_btime", 0)

        return {
            "wmPoiId": common_info.get("wmPoiId", ""),
            "wmOrderViewId": common_info.get("wm_order_id_view", ""),
            "wmPoiOrderDayseq": common_info.get("wm_poi_order_dayseq", ""),
            "confirmTime": common_info.get("confirmTime", 0),
            "deliveryBtime": delivery_btime,
            "isPreOrder": delivery_btime > 0  # 预订单标识
        }

    def get_orders_to_complete(self, meal_duration):
        """
        获取需要出餐的订单列表，优化预订单处理逻辑

        参数:
            meal_duration: 出餐时长（秒）

        返回:
            list: 需要出餐的订单列表
        """
        result = self.get_unprocessed_orders()
        if not result:
            raise Exception("API请求返回空结果，可能是网络问题或cookie失效")
        elif result.get("code") != 0:
            error_msg = result.get("msg", "未知错误")
            raise Exception(f"API返回错误 - code: {result.get('code')}, msg: {error_msg}")

        orders = result.get("data", {}).get("orderList") or []
        current_time = int(time.time())
        orders_to_complete = []

        for order in orders:
            order_data = self.extract_order_data(order)
            confirm_time = order_data.get("confirmTime", 0)
            delivery_btime = order_data.get("deliveryBtime", 0)
            is_preorder = order_data.get("isPreOrder", False)

            if is_preorder:
                # 获取预订单提醒时间（分钟）
                preorder_reminder_minutes = self.get_preorder_reminder_time()
                preorder_reminder_seconds = preorder_reminder_minutes * 60
                # 预订单：使用 delivery_btime - preOrderReminderTime 作为基准时间
                base_time = delivery_btime - preorder_reminder_seconds
                order_type = "预订单"
            else:
                # 普通订单：使用确认时间作为基准时间
                base_time = confirm_time
                order_type = "普通订单"

            # 检查是否需要出餐
            if base_time > 0 and (current_time - base_time) >= meal_duration:

                # 添加计算后的基准时间到订单数据中
                order_data["calculatedBaseTime"] = base_time
                order_data["orderType"] = order_type
                orders_to_complete.append(order_data)

        return orders_to_complete

def complete_meal_time(wm_order_view_id, wm_poi_id, cookie):
    """
    请求美团出餐时间接口
    
    参数:
        wm_order_view_id: 订单ID
        wm_poi_id: 商户ID
        cookie: 完整的cookie字符串或字典
    
    返回:
        响应结果的JSON数据
    """
    url = "https://e.waimai.meituan.com/v2/common/w/reported/completeMealTime"
    
    params = {
        "region_id": "1000440100",
        "region_version": "1651046402",
        "yodaReady": "h5",
        "csecplatform": "4",
        "csecversion": "3.2.1",
        "mtgsig": json.dumps({
            "a1": "1.2",
            "a2": int(time.time() * 1000),
            "a3": "5yy8v7592wx350u4y375w71zx80253w68061199v95u9795809z4zzu4",
            "a5": "tikCXPlcaIZq0OSxG+jeY4nQ4O/kl6BfGGEPU5gQcIE+kheBVV0U",
            "a6": "hs1.6W4irPBcoPdU0BGi4bsw5TOVPvbWdTLx0EIbrlHg3TjgYj0c7jD9YRZNjqB1bofTAeA3j0eUrQwO8TLadFtwMBf9+uxda+82WaJorfCJZo79n3tQ1MxWkGC+/X0m9v80Z",
            "a8": "e087fbcc53a40c07f89b60f06c7503f9",
            "a9": "3.2.1,7,162",
            "a10": "a6",
            "x0": 4,
            "d1": "aa0b00a65906850bf2b7880ff30d84b8"
        })
    }
    
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/x-www-form-urlencoded",
        "origin": "https://e.waimai.meituan.com",
        "referer": "https://e.waimai.meituan.com/new_fe/orderbusiness",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    }
    
    if isinstance(cookie, dict):
        cookie = "; ".join([f"{key}={value}" for key, value in cookie.items()])
    headers["cookie"] = cookie
    
    data = {
        "wmPoiId": wm_poi_id,
        "wmOrderViewId": wm_order_view_id
    }
    
    try:
        response = requests.post(
            url=url + "?" + urllib.parse.urlencode(params),
            headers=headers,
            data=data
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"请求失败: {e}")
        return {"error": f"请求失败: {e}"}

def parse_cookies(cookies_str):
    cookie_dict = {}
    if not cookies_str:
        return cookie_dict
        
    for item in cookies_str.split(';'):
        if item.strip():
            try:
                key, value = item.strip().split('=', 1)
                cookie_dict[key.strip()] = value.strip()
            except ValueError:
                pass
    return cookie_dict

def auto_complete_meal(cookies, meal_duration=360):
    """
    自动出餐函数 - 优化版本，支持预订单处理

    参数:
        cookies: Cookie字典或字符串
        meal_duration: 出餐时长（秒），只有达到此时间的订单才会出餐

    返回:
        (成功出餐的订单数量, 订单详情列表)
    """
    if isinstance(cookies, str):
        cookies = parse_cookies(cookies)

    orders_handler = MeituanUnprocessedOrders(cookies=cookies)
    orders_to_complete = orders_handler.get_orders_to_complete(meal_duration)

    if not orders_to_complete:
        return 0, []

    success_count = 0
    completed_orders = []

    for order in orders_to_complete:
        result = complete_meal_time(order["wmOrderViewId"], order["wmPoiId"], cookies)
        if result.get("code") == 0:
            print(f"✅ {order.get('orderType', '订单')} {order['wmOrderViewId']} 出餐成功")
            success_count += 1

            # 使用计算后的基准时间
            base_time = order.get("calculatedBaseTime", order.get("confirmTime", 0))
            current_time = int(time.time())
            meal_time_used = current_time - base_time if base_time > 0 else 0

            # 格式化时间
            order_time = datetime.fromtimestamp(base_time).strftime("%Y-%m-%d %H:%M:%S") if base_time > 0 else "未知"
            complete_time = datetime.fromtimestamp(current_time).strftime("%Y-%m-%d %H:%M:%S")

            # 记录订单详情
            order_detail = {
                "order_id": order['wmOrderViewId'],
                "wm_poi_order_dayseq": order.get('wmPoiOrderDayseq', ''),
                "order_type": order.get('orderType', '普通订单'),
                "order_time": order_time,
                "complete_time": complete_time,
                "meal_time_used": meal_time_used,
                "is_preorder": order.get('isPreOrder', False),
                "delivery_time": datetime.fromtimestamp(order.get('deliveryBtime', 0)).strftime("%Y-%m-%d %H:%M:%S") if order.get('deliveryBtime', 0) > 0 else None
            }
            completed_orders.append(order_detail)
        else:
            print(f"❌ {order.get('orderType', '订单')} {order['wmOrderViewId']} 出餐失败: {result.get('msg', '未知错误')}")

        time.sleep(0.5)  # 避免请求过快

    print(f"🎉 成功出餐 {success_count} 个订单")

    return success_count, completed_orders

if __name__ == "__main__":
    # 测试获取预订单提醒时间
    example_cookie = "_lxsdk_s=1984c6ade70-3ec-72c-ffd%7C%7C26; pushToken=01H6CmE4jO9ACX6CzfuVQfhq09pnOEFQvtqxeiaj2plw*; set_info=%7B%22wmPoiId%22%3A%2227124170%22%2C%22region_id%22%3A%************%22%2C%22region_version%22%3A1742440621%7D; set_info_single=%7B%22regionIdForSingle%22%3A%************%22%2C%22regionVersionForSingle%22%3A1742440621%7D; provinceId=410000; _lxsdk_cuid=1984c6ade6ec8-0df2ee3eb60172-26031d51-4b9600-1984c6ade6ec8; cityId=410900; region_version=1742440621; onlyForDaoDianAcct=0; _lxsdk=1984c6ade6ec8-0df2ee3eb60172-26031d51-4b9600-1984c6ade6ec8; region_id=1000320300; ignore_set_router_proxy=false; isChain=0; scIndex=0; acctId=227641160; city_id=320311; device_uuid=!715669da-6ce1-43f0-a3fb-4abdc539a12c; has_not_waimai_poi=0; location_id=320311; bsid=SgvG0hhR7Cfg7Ii3I3DaviFyMqXdG3ZMLR5VcuXIiYPHZbBWExBAs_Avm-yTaSKH2fpSzGOT0QwK6v9cu25Z8w; isOfflineSelfOpen=0; wmPoiId=27124170; JSESSIONID=8529kvxx2ebch0cfslcqah80; uuid_update=true; logan_session_token=l00r160h77yfrtwoa3eb; utm_source_rg=; token=01H6CmE4jO9ACX6CzfuVQfhq09pnOEFQvtqxeiaj2plw*; WEBDFPID=642473u9z25x5w9404u64z4485w9yxu38015x3zx00797958zw07w257-1753715129174-1753628725618EMAMOIMfd79fef3d01d5e9aadc18ccd4d0c95073757; shopCategory=food; wpush_server_url=wss://wpush.meituan.com; city_location_id=320300"

    print("🚀 开始自动出餐（优化版本 - 支持预订单处理）")
    print("=" * 60)

    success_count, completed_orders = auto_complete_meal(cookies=example_cookie, meal_duration=380)

    print("=" * 60)
    print(f"🎉 自动出餐完成，共成功出餐 {success_count} 个订单")

    if completed_orders:
        print("\n📋 订单详情:")
        for i, order in enumerate(completed_orders, 1):
            print(f"\n  {i}. 订单号: {order['order_id']}")
            print(f"     订单类型: {order['order_type']}")
            print(f"     基准时间: {order['order_time']}")
            print(f"     出餐时间: {order['complete_time']}")
            print(f"     出餐用时: {order['meal_time_used']}秒 ({order['meal_time_used']//60}分{order['meal_time_used']%60}秒)")
            if order.get('delivery_time'):
                print(f"     配送时间: {order['delivery_time']}")
    else:
        print("📝 暂无订单需要出餐")
