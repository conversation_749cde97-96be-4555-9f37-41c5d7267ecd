#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
带宽均衡配置和测试工具
帮助调整批次间隔参数，实现更平滑的带宽使用
"""

import sys
import os
import time
import math
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_bandwidth_pattern():
    """分析当前的带宽使用模式"""
    print("📊 带宽使用模式分析")
    print("=" * 60)
    
    try:
        from routes.task_routes import (
            REPLY_BATCH_INTERVAL,
            COMMENT_BATCH_INTERVAL, 
            MEAL_BATCH_INTERVAL,
            reply_executor,
            comment_executor,
            meal_executor
        )
        
        print(f"📋 当前批次间隔配置:")
        print(f"  自动回复: {REPLY_BATCH_INTERVAL} 秒")
        print(f"  自动回评: {COMMENT_BATCH_INTERVAL} 秒")
        print(f"  自动出餐: {MEAL_BATCH_INTERVAL} 秒")
        
        print(f"\n🔧 线程池配置:")
        print(f"  自动回复: {reply_executor._max_workers} 个线程")
        print(f"  自动回评: {comment_executor._max_workers} 个线程")
        print(f"  自动出餐: {meal_executor._max_workers} 个线程")
        
        # 计算理论分发时间
        print(f"\n⏱️ 理论分发时间 (假设100个店铺):")
        
        # 自动回复
        reply_batches = reply_executor._max_workers
        reply_total_time = reply_batches * REPLY_BATCH_INTERVAL
        print(f"  自动回复: {reply_batches} 批次 × {REPLY_BATCH_INTERVAL}秒 = {reply_total_time}秒")
        
        # 自动回评
        comment_batches = comment_executor._max_workers
        comment_total_time = comment_batches * COMMENT_BATCH_INTERVAL
        print(f"  自动回评: {comment_batches} 批次 × {COMMENT_BATCH_INTERVAL}秒 = {comment_total_time}秒")
        
        # 自动出餐
        meal_batches = meal_executor._max_workers
        meal_total_time = meal_batches * MEAL_BATCH_INTERVAL
        print(f"  自动出餐: {meal_batches} 批次 × {MEAL_BATCH_INTERVAL}秒 = {meal_total_time}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def simulate_bandwidth_usage():
    """模拟带宽使用情况"""
    print(f"\n📈 带宽使用模拟")
    print("=" * 60)
    
    try:
        from routes.task_routes import (
            REPLY_BATCH_INTERVAL,
            COMMENT_BATCH_INTERVAL, 
            MEAL_BATCH_INTERVAL,
            reply_executor,
            comment_executor,
            meal_executor
        )
        
        # 模拟参数
        shops_count = 100
        avg_request_size_kb = 2  # 每个请求平均2KB
        
        print(f"🎭 模拟参数:")
        print(f"  店铺数量: {shops_count}")
        print(f"  平均请求大小: {avg_request_size_kb}KB")
        
        # 模拟时间轴 (60秒内的带宽使用)
        timeline = {}
        
        # 自动回复分发模拟
        reply_shops_per_batch = math.ceil(shops_count / reply_executor._max_workers)
        for i in range(reply_executor._max_workers):
            dispatch_time = i * REPLY_BATCH_INTERVAL
            if dispatch_time not in timeline:
                timeline[dispatch_time] = 0
            timeline[dispatch_time] += reply_shops_per_batch * avg_request_size_kb
        
        # 自动回评分发模拟 (假设同时进行)
        comment_shops_per_batch = math.ceil(shops_count / comment_executor._max_workers)
        for i in range(comment_executor._max_workers):
            dispatch_time = i * COMMENT_BATCH_INTERVAL
            if dispatch_time not in timeline:
                timeline[dispatch_time] = 0
            timeline[dispatch_time] += comment_shops_per_batch * avg_request_size_kb
        
        # 自动出餐分发模拟
        meal_shops_per_batch = math.ceil(shops_count / meal_executor._max_workers)
        for i in range(meal_executor._max_workers):
            dispatch_time = i * MEAL_BATCH_INTERVAL
            if dispatch_time not in timeline:
                timeline[dispatch_time] = 0
            timeline[dispatch_time] += meal_shops_per_batch * avg_request_size_kb
        
        # 显示时间轴
        print(f"\n📊 带宽使用时间轴 (前10秒):")
        print(f"{'时间(秒)':<8} {'带宽(KB)':<10} {'可视化'}")
        print("-" * 40)
        
        max_bandwidth = max(timeline.values()) if timeline else 1
        for t in sorted(timeline.keys())[:20]:  # 只显示前20个时间点
            bandwidth = timeline[t]
            bar_length = int((bandwidth / max_bandwidth) * 20)
            bar = "█" * bar_length
            print(f"{t:<8.1f} {bandwidth:<10.1f} {bar}")
        
        # 计算峰谷比
        if timeline:
            peak = max(timeline.values())
            valley = min([v for v in timeline.values() if v > 0])
            peak_valley_ratio = peak / valley if valley > 0 else float('inf')
            
            print(f"\n📈 带宽统计:")
            print(f"  峰值: {peak:.1f}KB")
            print(f"  谷值: {valley:.1f}KB")
            print(f"  峰谷比: {peak_valley_ratio:.2f}")
            
            if peak_valley_ratio > 3:
                print(f"  ⚠️ 峰谷比较高，建议调整间隔参数")
            else:
                print(f"  ✅ 峰谷比合理")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        return False

def recommend_intervals():
    """推荐最佳间隔配置"""
    print(f"\n💡 间隔配置建议")
    print("=" * 60)
    
    try:
        from routes.task_routes import (
            reply_executor,
            comment_executor,
            meal_executor
        )
        
        # 基于线程数量推荐间隔
        print(f"🎯 基于线程池大小的推荐:")
        
        # 自动回复 (3个线程)
        reply_workers = reply_executor._max_workers
        reply_recommended = 0.3 + (reply_workers - 1) * 0.2  # 基础0.3秒 + 每增加1个线程增加0.2秒
        print(f"  自动回复 ({reply_workers}线程): {reply_recommended:.1f}秒")
        
        # 自动回评 (1个线程)
        comment_workers = comment_executor._max_workers
        comment_recommended = 0.2 + (comment_workers - 1) * 0.1
        print(f"  自动回评 ({comment_workers}线程): {comment_recommended:.1f}秒")
        
        # 自动出餐 (10个线程)
        meal_workers = meal_executor._max_workers
        meal_recommended = 0.5 + (meal_workers - 1) * 0.05  # 基础0.5秒 + 每增加1个线程增加0.05秒
        print(f"  自动出餐 ({meal_workers}线程): {meal_recommended:.1f}秒")
        
        print(f"\n🔧 不同场景的配置建议:")
        
        scenarios = [
            ("低带宽环境", {"reply": 0.8, "comment": 0.5, "meal": 1.2}),
            ("均衡使用", {"reply": 0.5, "comment": 0.3, "meal": 0.8}),
            ("高性能环境", {"reply": 0.3, "comment": 0.2, "meal": 0.5}),
            ("极致平滑", {"reply": 1.0, "comment": 0.6, "meal": 1.5})
        ]
        
        for scenario_name, config in scenarios:
            print(f"\n  📋 {scenario_name}:")
            print(f"    REPLY_BATCH_INTERVAL = {config['reply']}")
            print(f"    COMMENT_BATCH_INTERVAL = {config['comment']}")
            print(f"    MEAL_BATCH_INTERVAL = {config['meal']}")
            
            # 计算总分发时间
            total_time = max(
                reply_workers * config['reply'],
                comment_workers * config['comment'],
                meal_workers * config['meal']
            )
            print(f"    总分发时间: {total_time:.1f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 推荐失败: {e}")
        return False

def test_current_config():
    """测试当前配置的效果"""
    print(f"\n🧪 当前配置测试")
    print("=" * 60)
    
    try:
        from routes.task_routes import (
            REPLY_BATCH_INTERVAL,
            COMMENT_BATCH_INTERVAL, 
            MEAL_BATCH_INTERVAL
        )
        
        print(f"⏱️ 模拟分发过程:")
        
        # 模拟自动回复分发
        print(f"\n📤 自动回复分发 (间隔: {REPLY_BATCH_INTERVAL}秒):")
        for i in range(3):  # 3个线程
            dispatch_time = i * REPLY_BATCH_INTERVAL
            print(f"  {dispatch_time:.1f}秒: 分发批次 {i+1}")
        
        # 模拟自动回评分发
        print(f"\n📤 自动回评分发 (间隔: {COMMENT_BATCH_INTERVAL}秒):")
        for i in range(1):  # 1个线程
            dispatch_time = i * COMMENT_BATCH_INTERVAL
            print(f"  {dispatch_time:.1f}秒: 分发批次 {i+1}")
        
        # 模拟自动出餐分发
        print(f"\n📤 自动出餐分发 (间隔: {MEAL_BATCH_INTERVAL}秒):")
        for i in range(10):  # 10个线程
            dispatch_time = i * MEAL_BATCH_INTERVAL
            print(f"  {dispatch_time:.1f}秒: 分发批次 {i+1}")
        
        # 评估配置
        max_dispatch_time = max(
            3 * REPLY_BATCH_INTERVAL,
            1 * COMMENT_BATCH_INTERVAL,
            10 * MEAL_BATCH_INTERVAL
        )
        
        print(f"\n📊 配置评估:")
        print(f"  最长分发时间: {max_dispatch_time:.1f}秒")
        
        if max_dispatch_time < 5:
            print(f"  ✅ 分发速度快，带宽使用相对集中")
        elif max_dispatch_time < 10:
            print(f"  ⚖️ 分发速度适中，带宽使用较均衡")
        else:
            print(f"  🐌 分发速度慢，带宽使用很平滑")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 带宽均衡配置工具")
    
    # 1. 分析当前模式
    analyze_bandwidth_pattern()
    
    # 2. 模拟带宽使用
    simulate_bandwidth_usage()
    
    # 3. 推荐配置
    recommend_intervals()
    
    # 4. 测试当前配置
    test_current_config()
    
    print("\n" + "=" * 60)
    print("✅ 分析完成")
    
    print(f"\n📝 调整建议:")
    print(f"1. 如果带宽峰谷比 > 3，增加间隔时间")
    print(f"2. 如果分发时间 > 10秒，减少间隔时间")
    print(f"3. 根据网络环境选择合适的配置场景")
    print(f"4. 可以实时调整参数并观察效果")

if __name__ == "__main__":
    main()
