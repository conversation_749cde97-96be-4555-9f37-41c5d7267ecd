#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内存监控脚本
用于监控Python后端服务的内存使用情况
"""

import psutil
import time
import os
import sys
from datetime import datetime

def get_process_by_name(process_name):
    """根据进程名查找进程"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            # 检查进程名或命令行参数
            if (process_name.lower() in proc.info['name'].lower() or 
                any(process_name.lower() in arg.lower() for arg in proc.info['cmdline'] if arg)):
                return proc
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def monitor_memory(process_name="python", interval=10, duration=3600):
    """
    监控指定进程的内存使用
    
    Args:
        process_name: 进程名称（默认python）
        interval: 监控间隔（秒）
        duration: 监控时长（秒，默认1小时）
    """
    print(f"开始监控进程: {process_name}")
    print(f"监控间隔: {interval}秒")
    print(f"监控时长: {duration}秒")
    print("-" * 60)
    
    start_time = time.time()
    max_memory = 0
    min_memory = float('inf')
    memory_readings = []
    
    while time.time() - start_time < duration:
        try:
            # 查找进程
            process = get_process_by_name(process_name)
            
            if process is None:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 未找到进程: {process_name}")
                time.sleep(interval)
                continue
            
            # 获取内存信息
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB
            memory_percent = process.memory_percent()
            
            # 更新统计
            max_memory = max(max_memory, memory_mb)
            min_memory = min(min_memory, memory_mb)
            memory_readings.append(memory_mb)
            
            # 计算平均值
            avg_memory = sum(memory_readings) / len(memory_readings)
            
            # 检查内存增长趋势
            trend = ""
            if len(memory_readings) >= 5:
                recent_avg = sum(memory_readings[-5:]) / 5
                older_avg = sum(memory_readings[-10:-5]) / 5 if len(memory_readings) >= 10 else recent_avg
                if recent_avg > older_avg * 1.05:  # 增长超过5%
                    trend = " ⚠️ 内存增长"
                elif recent_avg < older_avg * 0.95:  # 减少超过5%
                    trend = " ✅ 内存减少"
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] "
                  f"PID: {process.pid} | "
                  f"内存: {memory_mb:.1f}MB ({memory_percent:.1f}%) | "
                  f"最大: {max_memory:.1f}MB | "
                  f"平均: {avg_memory:.1f}MB{trend}")
            
            # 内存泄漏警告
            if memory_mb > 1000:  # 超过1GB
                print(f"⚠️  警告: 内存使用过高 ({memory_mb:.1f}MB)")
            
            # 如果内存持续增长，发出警告
            if len(memory_readings) >= 10:
                recent_trend = memory_readings[-1] - memory_readings[-10]
                if recent_trend > 100:  # 10次检查内存增长超过100MB
                    print(f"🚨 内存泄漏警告: 最近10次检查内存增长了 {recent_trend:.1f}MB")
            
        except psutil.NoSuchProcess:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 进程已结束")
            break
        except Exception as e:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 监控错误: {e}")
        
        time.sleep(interval)
    
    # 输出总结
    print("-" * 60)
    print("监控总结:")
    print(f"最大内存使用: {max_memory:.1f}MB")
    print(f"最小内存使用: {min_memory:.1f}MB")
    print(f"平均内存使用: {sum(memory_readings) / len(memory_readings):.1f}MB")
    print(f"内存变化: {memory_readings[-1] - memory_readings[0]:.1f}MB")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        process_name = sys.argv[1]
    else:
        process_name = "python"
    
    try:
        monitor_memory(process_name, interval=5, duration=3600)  # 监控1小时
    except KeyboardInterrupt:
        print("\n监控已停止")
