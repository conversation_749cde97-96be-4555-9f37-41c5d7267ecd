#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化管理员客户端 - 使用CustomTkinter
需要安装: pip install customtkinter
"""

import sys
import json
import requests
import threading
import tkinter as tk
from tkinter import messagebox, ttk
import customtkinter as ctk
from datetime import datetime
import hashlib
import pickle
import os

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")  # 浅色主题
ctk.set_default_color_theme("blue")  # 蓝色主题

API_BASE = "http://[2408:8220:5815:5ca0:4e09:1c4c:cc2a:ee5d]:5000"

class ModernAdminClient(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        # 配置主窗口
        self.title("美团外卖管理系统")
        self.geometry("1400x900")
        self.minsize(1200, 700)
        
        # 数据存储
        self.admin_info = None
        self.agents_data = []
        self.filtered_data = []
        self.session_file = "admin_session.pkl"
        
        # 初始化界面
        self.init_ui()
        
        # 尝试自动登录
        self.try_auto_login()
        
    def init_ui(self):
        """初始化界面"""
        # 创建主框架
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 创建登录框架
        self.login_frame = ctk.CTkFrame(self.main_frame)
        self.login_frame.pack(fill="both", expand=True)
        
        # 创建主内容框架（初始隐藏）
        self.content_frame = ctk.CTkFrame(self.main_frame)
        
        # 初始化登录界面
        self.init_login_ui()
        
        # 初始化主界面
        self.init_main_ui()
        
    def init_login_ui(self):
        """初始化登录界面"""
        # 登录标题
        title_label = ctk.CTkLabel(
            self.login_frame, 
            text="美团外卖管理系统", 
            font=ctk.CTkFont(size=32, weight="bold")
        )
        title_label.pack(pady=(50, 20))
        
        subtitle_label = ctk.CTkLabel(
            self.login_frame, 
            text="管理员登录", 
            font=ctk.CTkFont(size=18),
            text_color="gray"
        )
        subtitle_label.pack(pady=(0, 40))
        
        # 登录表单框架
        form_frame = ctk.CTkFrame(self.login_frame, fg_color="transparent")
        form_frame.pack(pady=20)
        
        # 账号输入
        self.account_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="请输入管理员账号",
            width=400,
            height=50,
            font=ctk.CTkFont(size=16)
        )
        self.account_entry.pack(pady=10)
        
        # 密码输入
        self.password_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="请输入密码",
            show="*",
            width=400,
            height=50,
            font=ctk.CTkFont(size=16)
        )
        self.password_entry.pack(pady=10)
        
        # 记住登录选项
        self.remember_var = ctk.BooleanVar()
        remember_checkbox = ctk.CTkCheckBox(
            form_frame,
            text="记住登录状态",
            variable=self.remember_var,
            font=ctk.CTkFont(size=14)
        )
        remember_checkbox.pack(pady=10)
        
        # 登录按钮
        self.login_button = ctk.CTkButton(
            form_frame,
            text="登录",
            width=400,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            command=self.handle_login
        )
        self.login_button.pack(pady=20)
        
        # 状态标签
        self.status_label = ctk.CTkLabel(
            form_frame,
            text="",
            font=ctk.CTkFont(size=14),
            text_color="red"
        )
        self.status_label.pack(pady=10)
        
        # 绑定回车键
        self.account_entry.bind("<Return>", lambda e: self.handle_login())
        self.password_entry.bind("<Return>", lambda e: self.handle_login())
        
    def init_main_ui(self):
        """初始化主界面"""
        # 顶部工具栏
        toolbar_frame = ctk.CTkFrame(self.content_frame)
        toolbar_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        # 左侧标题
        title_label = ctk.CTkLabel(
            toolbar_frame,
            text="代理管理系统",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=20)
        
        # 右侧管理员信息
        self.admin_info_label = ctk.CTkLabel(
            toolbar_frame,
            text="未登录",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        self.admin_info_label.pack(side="right", padx=20, pady=20)
        
        # 搜索和操作区域
        search_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        search_frame.pack(fill="x", padx=20, pady=10)
        
        # 搜索框
        ctk.CTkLabel(search_frame, text="搜索:", font=ctk.CTkFont(size=14)).pack(side="left", padx=(0, 10))
        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="输入代理账号搜索",
            width=300,
            height=35,
            font=ctk.CTkFont(size=14)
        )
        self.search_entry.pack(side="left", padx=(0, 10))
        
        # 搜索按钮
        self.search_button = ctk.CTkButton(
            search_frame,
            text="🔍 搜索",
            width=80,
            height=35,
            font=ctk.CTkFont(size=12),
            command=self.filter_agents
        )
        self.search_button.pack(side="left", padx=(0, 20))
        
        # 清空搜索按钮
        self.clear_search_button = ctk.CTkButton(
            search_frame,
            text="❌ 清空",
            width=80,
            height=35,
            font=ctk.CTkFont(size=12),
            command=self.clear_search,
            fg_color="gray",
            hover_color="darkgray"
        )
        self.clear_search_button.pack(side="left")
        
        # 操作按钮区域
        button_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=20, pady=10)
        
        # 添加代理按钮
        self.add_button = ctk.CTkButton(
            button_frame,
            text="➕ 添加代理",
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.add_agent
        )
        self.add_button.pack(side="left", padx=(0, 10))
        
        # 刷新按钮
        self.refresh_button = ctk.CTkButton(
            button_frame,
            text="🔄 刷新",
            width=100,
            height=40,
            font=ctk.CTkFont(size=14),
            command=self.refresh_agents
        )
        self.refresh_button.pack(side="left", padx=(0, 10))
        
        # 登出按钮
        self.logout_button = ctk.CTkButton(
            button_frame,
            text="🚪 登出",
            width=100,
            height=40,
            font=ctk.CTkFont(size=14),
            command=self.logout,
            fg_color="red",
            hover_color="darkred"
        )
        self.logout_button.pack(side="right")
        
        # 创建卡片容器
        self.create_card_container()
        
        # 状态栏
        self.status_label_main = ctk.CTkLabel(
            self.content_frame,
            text="等待操作...",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        self.status_label_main.pack(side="bottom", pady=10)
        
    def create_card_container(self):
        """创建卡片容器"""
        # 卡片容器框架
        self.card_container = ctk.CTkScrollableFrame(
            self.content_frame,
            label_text="代理列表",
            label_font=ctk.CTkFont(size=16, weight="bold")
        )
        self.card_container.pack(fill="both", expand=True, padx=20, pady=10)
        
    def create_agent_card(self, agent):
        """创建代理卡片"""
        # 卡片框架
        card_frame = ctk.CTkFrame(self.card_container)
        card_frame.pack(fill="x", padx=10, pady=5)
        
        # 卡片内容
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=15)
        
        # 左侧信息
        info_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        info_frame.pack(side="left", fill="both", expand=True)
        
        # 代理账号
        account_label = ctk.CTkLabel(
            info_frame,
            text=f"账号: {agent['agent_account']}",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="#0078d4"
        )
        account_label.pack(anchor="w", pady=(0, 5))
        
        # 积分
        balance = int(agent['balance']) if agent['balance'] is not None else 0
        balance_label = ctk.CTkLabel(
            info_frame,
            text=f"积分: {balance}",
            font=ctk.CTkFont(size=14),
            text_color="#28a745"
        )
        balance_label.pack(anchor="w", pady=(0, 5))
        
        # 创建时间
        created_label = ctk.CTkLabel(
            info_frame,
            text=f"创建: {agent['created_at']}",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        created_label.pack(anchor="w")
        
        # 右侧操作按钮
        button_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        button_frame.pack(side="right", padx=(10, 0))
        
        # 增加积分按钮
        add_balance_btn = ctk.CTkButton(
            button_frame,
            text="➕ 增加积分",
            width=100,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),
            command=lambda: self.quick_add_balance(agent),
            fg_color="#28a745",
            hover_color="#218838"
        )
        add_balance_btn.pack(pady=5)
        
        # 删除按钮
        delete_btn = ctk.CTkButton(
            button_frame,
            text="🗑️ 删除",
            width=100,
            height=35,
            font=ctk.CTkFont(size=12),
            command=lambda: self.delete_agent(agent),
            fg_color="#dc3545",
            hover_color="#c82333"
        )
        delete_btn.pack(pady=5)
        
    def filter_agents(self, event=None):
        """筛选代理"""
        search_text = self.search_entry.get().lower().strip()
        
        if not search_text:
            self.filtered_data = self.agents_data.copy()
        else:
            self.filtered_data = [
                agent for agent in self.agents_data 
                if search_text in agent['agent_account'].lower()
            ]
        
        self.update_cards_display()
        
    def clear_search(self):
        """清空搜索"""
        self.search_entry.delete(0, "end")
        self.filtered_data = self.agents_data.copy()
        self.update_cards_display()
        
    def try_auto_login(self):
        """尝试自动登录"""
        if os.path.exists(self.session_file):
            try:
                with open(self.session_file, 'rb') as f:
                    session_data = pickle.load(f)
                    if session_data.get('remember', False):
                        self.account_entry.insert(0, session_data.get('account', ''))
                        self.password_entry.insert(0, session_data.get('password', ''))
                        self.remember_var.set(True)
            except:
                pass
                
    def save_session(self):
        """保存登录会话"""
        if self.remember_var.get():
            session_data = {
                'account': self.account_entry.get(),
                'password': self.password_entry.get(),
                'remember': True
            }
            with open(self.session_file, 'wb') as f:
                pickle.dump(session_data, f)
        else:
            if os.path.exists(self.session_file):
                os.remove(self.session_file)
                
    def handle_login(self):
        """处理登录"""
        account = self.account_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not account or not password:
            self.status_label.configure(text="请输入账号和密码")
            return
            
        # 禁用登录按钮
        self.login_button.configure(text="登录中...", state="disabled")
        self.status_label.configure(text="正在验证账号...")
        
        # 在新线程中执行登录
        def login_thread():
            try:
                response = requests.post(
                    f"{API_BASE}/api/admin/login",
                    json={
                        'admin_account': account,
                        'admin_password': password
                    },
                    headers={'Content-Type': 'application/json'},
                    timeout=5
                )
                
                result = response.json()
                
                # 在主线程中更新UI
                self.after(0, lambda: self.on_login_result(result, account))
                
            except Exception as e:
                self.after(0, lambda: self.on_login_error(str(e)))
                
        threading.Thread(target=login_thread, daemon=True).start()
        
    def on_login_result(self, result, account):
        """登录结果处理"""
        if result['code'] == 0:
            self.admin_info = result['data']['admin']
            self.save_session()
            self.show_main_interface()
            # 移除登录成功对话框
        else:
            self.status_label.configure(text=result['message'] or "登录失败")
            self.login_button.configure(text="登录", state="normal")
            
    def on_login_error(self, error):
        """登录错误处理"""
        self.status_label.configure(text=f"登录失败: {error}")
        self.login_button.configure(text="登录", state="normal")
        
    def show_main_interface(self):
        """显示主界面"""
        self.login_frame.pack_forget()
        self.content_frame.pack(fill="both", expand=True)
        self.admin_info_label.configure(text=f"管理员: {self.admin_info['admin_account']}")
        self.refresh_agents()
        
    def logout(self):
        """登出"""
        reply = messagebox.askyesno("确认登出", "确定要登出吗？")
        if reply:
            self.admin_info = None
            self.content_frame.pack_forget()
            self.login_frame.pack(fill="both", expand=True)
            self.admin_info_label.configure(text="未登录")
            self.account_entry.delete(0, "end")
            self.password_entry.delete(0, "end")
            self.remember_var.set(False)
            self.status_label.configure(text="")
            
    def refresh_agents(self):
        """刷新代理列表"""
        self.status_label_main.configure(text="正在获取代理列表...")
        
        def refresh_thread():
            try:
                headers = {
                    'Content-Type': 'application/json',
                    'X-Admin-Token': 'meituan_admin_2024_secure'
                }
                response = requests.get(f"{API_BASE}/api/admin/agent_list", 
                                     headers=headers, timeout=5)
                
                if response.status_code == 200:
                    result = response.json()
                    if result['code'] == 0:
                        self.after(0, lambda: self.update_cards(result['data']))
                    else:
                        self.after(0, lambda: self.status_label_main.configure(text=f"获取失败: {result['message']}"))
                else:
                    self.after(0, lambda: self.status_label_main.configure(text=f"请求失败: {response.status_code}"))
                    
            except Exception as e:
                self.after(0, lambda: self.status_label_main.configure(text=f"获取失败: {str(e)}"))
                
        threading.Thread(target=refresh_thread, daemon=True).start()
        
    def update_cards(self, agents_data):
        """更新卡片数据"""
        self.agents_data = agents_data
        self.filtered_data = agents_data.copy()
        self.update_cards_display()
        
    def update_cards_display(self):
        """更新卡片显示"""
        # 清空现有卡片
        for widget in self.card_container.winfo_children():
            widget.destroy()
            
        # 创建新卡片
        for agent in self.filtered_data:
            self.create_agent_card(agent)
            
        self.status_label_main.configure(text=f"共 {len(self.filtered_data)} 个代理")
        
    def quick_add_balance(self, agent):
        """快速增加积分"""
        dialog = QuickAddBalanceDialog(self, agent)
        self.wait_window(dialog)
        if dialog.result:
            self.refresh_agents()
            
    def delete_agent(self, agent):
        """删除代理"""
        reply = messagebox.askyesno("确认删除", f"确定要删除代理 {agent['agent_account']} 吗？\n此操作不可恢复！")
        if reply:
            def delete_thread():
                try:
                    headers = {
                        'Content-Type': 'application/json',
                        'X-Admin-Token': 'meituan_admin_2024_secure'
                    }
                    response = requests.delete(
                        f"{API_BASE}/api/admin/agent_delete/{agent['id']}",
                        headers=headers,
                        timeout=5
                    )
                    
                    result = response.json()
                    
                    if result['code'] == 0:
                        self.after(0, lambda: self.on_delete_success())
                    else:
                        self.after(0, lambda: messagebox.showerror("错误", result['message']))
                        
                except Exception as e:
                    self.after(0, lambda: messagebox.showerror("错误", f"删除失败: {str(e)}"))
                    
            threading.Thread(target=delete_thread, daemon=True).start()
            
    def on_delete_success(self):
        """删除成功"""
        messagebox.showinfo("成功", "代理删除成功！")
        self.refresh_agents()
            
    def add_agent(self):
        """添加代理"""
        dialog = AddAgentDialog(self)
        self.wait_window(dialog)
        if dialog.result:
            self.refresh_agents()

class AddAgentDialog(ctk.CTkToplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.result = None
        
        # 配置窗口
        self.title("添加代理")
        self.geometry("500x450")
        self.resizable(False, False)
        
        # 设置模态
        self.transient(parent)
        self.grab_set()
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        # 标题
        title_label = ctk.CTkLabel(
            self,
            text="添加新代理",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 代理账号
        ctk.CTkLabel(self, text="代理账号:", font=ctk.CTkFont(size=16)).pack(anchor="w", padx=40, pady=(10, 5))
        self.account_entry = ctk.CTkEntry(
            self,
            placeholder_text="请输入代理账号",
            width=400,
            height=45,
            font=ctk.CTkFont(size=16)
        )
        self.account_entry.pack(pady=(0, 20))
        
        # 密码
        ctk.CTkLabel(self, text="密码:", font=ctk.CTkFont(size=16)).pack(anchor="w", padx=40, pady=(0, 5))
        self.password_entry = ctk.CTkEntry(
            self,
            placeholder_text="请输入密码",
            show="*",
            width=400,
            height=45,
            font=ctk.CTkFont(size=16)
        )
        self.password_entry.pack(pady=(0, 20))
        
        # 初始积分
        ctk.CTkLabel(self, text="初始积分:", font=ctk.CTkFont(size=16)).pack(anchor="w", padx=40, pady=(0, 5))
        self.balance_entry = ctk.CTkEntry(
            self,
            placeholder_text="0",
            width=400,
            height=45,
            font=ctk.CTkFont(size=16)
        )
        self.balance_entry.pack(pady=(0, 30))
        self.balance_entry.insert(0, "0")
        
        # 按钮框架
        button_frame = ctk.CTkFrame(self, fg_color="transparent")
        button_frame.pack(fill="x", padx=40, pady=20)
        
        # 取消按钮
        self.cancel_button = ctk.CTkButton(
            button_frame,
            text="取消",
            width=120,
            height=40,
            font=ctk.CTkFont(size=14),
            command=self.cancel,
            fg_color="gray",
            hover_color="darkgray"
        )
        self.cancel_button.pack(side="left", padx=(0, 10))
        
        # 确认按钮
        self.confirm_button = ctk.CTkButton(
            button_frame,
            text="确认添加",
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.confirm
        )
        self.confirm_button.pack(side="right")
            
    def confirm(self):
        """确认操作"""
        account = self.account_entry.get().strip()
        password = self.password_entry.get().strip()
        balance = self.balance_entry.get().strip()
        
        if not account:
            messagebox.showerror("错误", "请输入代理账号")
            return
            
        if not password:
            messagebox.showerror("错误", "请输入密码")
            return
            
        try:
            balance = int(balance) if balance else 0
        except ValueError:
            messagebox.showerror("错误", "积分必须是整数")
            return
            
        # 准备数据
        data = {
            'agent_account': account,
            'agent_password': password,
            'balance': balance
        }
        
        # 在新线程中执行操作
        def operation_thread():
            try:
                headers = {
                    'Content-Type': 'application/json',
                    'X-Admin-Token': 'meituan_admin_2024_secure'
                }
                
                response = requests.post(
                    f"{API_BASE}/api/admin/agent_create",
                    json=data,
                    headers=headers,
                    timeout=5
                )
                    
                result = response.json()
                
                if result['code'] == 0:
                    self.after(0, lambda: self.on_success())
                else:
                    self.after(0, lambda: messagebox.showerror("错误", result['message']))
                    
            except Exception as e:
                self.after(0, lambda: messagebox.showerror("错误", f"操作失败: {str(e)}"))
                
        threading.Thread(target=operation_thread, daemon=True).start()
        
    def on_success(self):
        """操作成功"""
        self.result = True
        messagebox.showinfo("成功", "代理添加成功！")
        self.destroy()
        
    def cancel(self):
        """取消操作"""
        self.destroy()

class QuickAddBalanceDialog(ctk.CTkToplevel):
    def __init__(self, parent, agent):
        super().__init__(parent)
        self.parent = parent
        self.agent = agent
        self.result = None
        
        # 配置窗口
        self.title("快速增加积分")
        self.geometry("450x400")
        self.resizable(False, False)
        
        # 设置模态
        self.transient(parent)
        self.grab_set()
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        # 标题
        title_label = ctk.CTkLabel(
            self,
            text="快速增加积分",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 代理信息
        info_label = ctk.CTkLabel(
            self,
            text=f"代理账号: {self.agent['agent_account']}",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        info_label.pack(pady=10)
        
        # 当前积分
        current_balance = int(self.agent['balance']) if self.agent['balance'] is not None else 0
        current_label = ctk.CTkLabel(
            self,
            text=f"当前积分: {current_balance}",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="green"
        )
        current_label.pack(pady=20)
        
        # 增加积分输入
        ctk.CTkLabel(self, text="增加积分:", font=ctk.CTkFont(size=16)).pack(anchor="w", padx=40, pady=(20, 5))
        self.add_balance_entry = ctk.CTkEntry(
            self,
            placeholder_text="请输入要增加的积分",
            width=320,
            height=45,
            font=ctk.CTkFont(size=16)
        )
        self.add_balance_entry.pack(pady=(0, 30))
        self.add_balance_entry.insert(0, "100")
        
        # 按钮框架
        button_frame = ctk.CTkFrame(self, fg_color="transparent")
        button_frame.pack(fill="x", padx=40, pady=20)
        
        # 取消按钮
        self.cancel_button = ctk.CTkButton(
            button_frame,
            text="取消",
            width=120,
            height=40,
            font=ctk.CTkFont(size=14),
            command=self.cancel,
            fg_color="gray",
            hover_color="darkgray"
        )
        self.cancel_button.pack(side="left", padx=(0, 10))
        
        # 确认按钮
        self.confirm_button = ctk.CTkButton(
            button_frame,
            text="确认增加",
            width=120,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            command=self.confirm
        )
        self.confirm_button.pack(side="right")
        
    def confirm(self):
        """确认增加积分"""
        try:
            add_balance = int(self.add_balance_entry.get().strip())
            if add_balance <= 0:
                messagebox.showerror("错误", "增加的积分必须大于0")
                return
        except ValueError:
            messagebox.showerror("错误", "积分必须是整数")
            return
            
        # 计算新积分
        current_balance = int(self.agent['balance']) if self.agent['balance'] is not None else 0
        new_balance = current_balance + add_balance
            
        # 在新线程中执行更新
        def update_thread():
            try:
                headers = {
                    'Content-Type': 'application/json',
                    'X-Admin-Token': 'meituan_admin_2024_secure'
                }
                
                response = requests.put(
                    f"{API_BASE}/api/admin/agent_update/{self.agent['id']}",
                    json={'balance': new_balance},
                    headers=headers,
                    timeout=5
                )
                
                result = response.json()
                
                if result['code'] == 0:
                    self.after(0, lambda: self.on_success())
                else:
                    self.after(0, lambda: messagebox.showerror("错误", result['message']))
                    
            except Exception as e:
                self.after(0, lambda: messagebox.showerror("错误", f"更新失败: {str(e)}"))
                
        threading.Thread(target=update_thread, daemon=True).start()
        
    def on_success(self):
        """更新成功"""
        self.result = True
        messagebox.showinfo("成功", "积分增加成功！")
        self.destroy()
        
    def cancel(self):
        """取消操作"""
        self.destroy()

if __name__ == "__main__":
    app = ModernAdminClient()
    app.mainloop() 