#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试自动回评时间间隔控制
验证自动回评的分发频率限制功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routes.task_routes import (
    is_executor_busy, 
    comment_executor,
    task_progress,
    task_progress_lock,
    COMMENT_DISPATCH_INTERVAL
)

def test_comment_interval_control():
    """测试自动回评时间间隔控制"""
    print("🕐 测试自动回评时间间隔控制")
    print("=" * 60)
    
    # 显示当前配置
    print(f"📋 当前配置:")
    print(f"  自动回评分发间隔: {COMMENT_DISPATCH_INTERVAL} 秒 ({COMMENT_DISPATCH_INTERVAL//60} 分钟)")
    print(f"  线程池配置: {comment_executor._max_workers} 个工作线程")
    
    # 模拟时间戳
    current_time = time.time()
    last_comment_dispatch_time = 0  # 模拟从未分发过
    
    print(f"\n🎭 模拟场景测试:")
    
    # 场景1：首次分发（从未分发过）
    print(f"\n📍 场景1：首次分发")
    with task_progress_lock:
        task_progress['auto_comment']['total'] = 0  # 没有正在执行的任务
        task_progress['auto_comment']['completed'] = 0
    
    time_since_last = current_time - last_comment_dispatch_time
    can_dispatch = time_since_last >= COMMENT_DISPATCH_INTERVAL
    
    print(f"  距离上次分发: {int(time_since_last)} 秒")
    print(f"  需要间隔: {COMMENT_DISPATCH_INTERVAL} 秒")
    print(f"  可以分发: {'✅ 是' if can_dispatch else '❌ 否'}")
    
    if can_dispatch:
        last_comment_dispatch_time = current_time
        print(f"  📤 分发自动回评任务: 100 个店铺 (下次分发: {COMMENT_DISPATCH_INTERVAL//60}分钟后)")
    
    # 场景2：5分钟后尝试分发（应该被拒绝）
    print(f"\n📍 场景2：5分钟后尝试分发")
    current_time += 300  # 5分钟后
    
    with task_progress_lock:
        task_progress['auto_comment']['total'] = 100
        task_progress['auto_comment']['completed'] = 100  # 任务已完成
    
    time_since_last = current_time - last_comment_dispatch_time
    can_dispatch = time_since_last >= COMMENT_DISPATCH_INTERVAL
    
    print(f"  距离上次分发: {int(time_since_last)} 秒 ({int(time_since_last/60)} 分钟)")
    print(f"  需要间隔: {COMMENT_DISPATCH_INTERVAL} 秒 ({COMMENT_DISPATCH_INTERVAL//60} 分钟)")
    print(f"  可以分发: {'✅ 是' if can_dispatch else '❌ 否'}")
    
    if not can_dispatch:
        remaining_time = COMMENT_DISPATCH_INTERVAL - time_since_last
        remaining_minutes = int(remaining_time / 60)
        print(f"  ⏰ 自动回评距离下次分发还需等待 {remaining_minutes} 分钟")
    
    # 场景3：1小时后尝试分发（应该可以分发）
    print(f"\n📍 场景3：1小时后尝试分发")
    current_time += 3600  # 1小时后
    
    time_since_last = current_time - last_comment_dispatch_time
    can_dispatch = time_since_last >= COMMENT_DISPATCH_INTERVAL
    
    print(f"  距离上次分发: {int(time_since_last)} 秒 ({int(time_since_last/60)} 分钟)")
    print(f"  需要间隔: {COMMENT_DISPATCH_INTERVAL} 秒 ({COMMENT_DISPATCH_INTERVAL//60} 分钟)")
    print(f"  可以分发: {'✅ 是' if can_dispatch else '❌ 否'}")
    
    if can_dispatch:
        last_comment_dispatch_time = current_time
        print(f"  📤 分发自动回评任务: 150 个店铺 (下次分发: {COMMENT_DISPATCH_INTERVAL//60}分钟后)")

def test_different_intervals():
    """测试不同时间间隔的效果"""
    print(f"\n🔧 不同时间间隔的效果对比:")
    print("=" * 60)
    
    intervals = [
        (300, "5分钟"),
        (900, "15分钟"), 
        (1800, "30分钟"),
        (3600, "1小时"),
        (7200, "2小时"),
        (21600, "6小时")
    ]
    
    print(f"{'间隔设置':<10} {'说明':<15} {'每日最大分发次数':<15} {'适用场景'}")
    print("-" * 60)
    
    for seconds, desc in intervals:
        daily_max = 24 * 3600 // seconds
        if seconds <= 900:
            scenario = "高频回评"
        elif seconds <= 3600:
            scenario = "正常回评"
        else:
            scenario = "低频回评"
        
        print(f"{seconds:<10} {desc:<15} {daily_max:<15} {scenario}")
    
    print(f"\n💡 建议:")
    print(f"  - 高流量店铺: 15-30分钟间隔")
    print(f"  - 普通店铺: 1-2小时间隔 (当前设置)")
    print(f"  - 低频店铺: 6小时间隔")
    print(f"  - 节省带宽: 增加间隔时间")

def show_bandwidth_impact():
    """显示带宽影响分析"""
    print(f"\n📊 带宽影响分析:")
    print("=" * 60)
    
    # 假设参数
    shops_count = 100
    avg_request_size = 2  # KB per request
    
    intervals = [300, 1800, 3600, 7200]  # 5分钟, 30分钟, 1小时, 2小时
    
    print(f"假设条件: {shops_count}个店铺, 每次请求约{avg_request_size}KB")
    print(f"{'间隔':<10} {'每日分发次数':<12} {'每日带宽使用':<15} {'每月带宽使用'}")
    print("-" * 60)
    
    for interval in intervals:
        daily_dispatches = 24 * 3600 // interval
        daily_bandwidth_kb = daily_dispatches * shops_count * avg_request_size
        daily_bandwidth_mb = daily_bandwidth_kb / 1024
        monthly_bandwidth_gb = daily_bandwidth_mb * 30 / 1024
        
        interval_desc = f"{interval//60}分钟" if interval < 3600 else f"{interval//3600}小时"
        
        print(f"{interval_desc:<10} {daily_dispatches:<12} {daily_bandwidth_mb:.1f}MB{'':<8} {monthly_bandwidth_gb:.2f}GB")
    
    print(f"\n🎯 当前设置 ({COMMENT_DISPATCH_INTERVAL//60}分钟间隔) 的优势:")
    current_daily = 24 * 3600 // COMMENT_DISPATCH_INTERVAL
    current_bandwidth = current_daily * shops_count * avg_request_size / 1024
    print(f"  - 每日分发次数: {current_daily} 次")
    print(f"  - 每日带宽使用: {current_bandwidth:.1f}MB")
    print(f"  - 既保证了回评效果，又大幅节省了带宽")

def main():
    """主测试函数"""
    print("🚀 自动回评时间间隔控制测试")
    
    # 1. 测试时间间隔控制逻辑
    test_comment_interval_control()
    
    # 2. 测试不同间隔的效果
    test_different_intervals()
    
    # 3. 显示带宽影响
    show_bandwidth_impact()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    
    print(f"\n📋 功能总结:")
    print(f"1. ✅ 添加了自动回评独立的时间间隔控制")
    print(f"2. ✅ 默认设置为1小时间隔，大幅节省带宽")
    print(f"3. ✅ 保持了精细化进度跟踪的所有功能")
    print(f"4. ✅ 可以通过修改 COMMENT_DISPATCH_INTERVAL 调整间隔")
    print(f"5. ✅ 提供了清晰的等待时间提示")

if __name__ == "__main__":
    main()
