#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
店铺管理路由
"""

from flask import Blueprint, request, jsonify
from utils.db_utils import get_connection
from datetime import datetime
import uuid
import random

shop_bp = Blueprint('shop', __name__)

@shop_bp.route('/init', methods=['POST'])
def init_shop():
    """初始化店铺信息"""
    connection = None
    cursor = None
    try:
        data = request.get_json()
        
        # 检查必填字段
        required_fields = ['cookie', 'shop_id', 'login_account', 'shop_name']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({
                    'code': 1,
                    'message': f'缺少必填字段: {field}',
                    'data': None
                }), 400
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查店铺是否已存在
        check_query = "SELECT id FROM shops WHERE shop_id = %s"
        cursor.execute(check_query, [data['shop_id']])
        existing_shop = cursor.fetchone()
        if existing_shop:
            # 只更新 cookie 字段
            update_query = "UPDATE shops SET cookie = %s, updated_at = NOW() , ck_status =1 WHERE id = %s"
            cursor.execute(update_query, [data['cookie'], existing_shop['id']])
            connection.commit()
            return jsonify({
                'code': 0,
                'message': '店铺已存在，已更新cookie',
                'data': {
                    'shop_id': data['shop_id']
                }
            })
        
        # 生成 sender_uid 和 device_id
        sender_uid = ''.join([str(random.randint(0,9)) for _ in range(10)])
        device_id = '!' + str(uuid.uuid4())
        
        # 插入店铺信息
        insert_query = """
        INSERT INTO shops (
            shop_id, login_account, shop_name, cookie, logo, 
            agent_id, agent_account, sender_uid, device_id, created_at, updated_at
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """
        
        cursor.execute(insert_query, [
            data['shop_id'],
            data['login_account'],
            data['shop_name'],
            data['cookie'],
            data.get('logo', ''),
            data.get('agent_id', None),  # 代理ID
            data.get('agent_account', ''),  # 代理账号
            sender_uid,
            device_id
        ])
        
        connection.commit()
        
        # 获取创建的店铺信息
        shop_id = cursor.lastrowid
        cursor.execute("SELECT * FROM shops WHERE id = %s", [shop_id])
        shop = cursor.fetchone()
        
        # 处理时间格式
        if shop['created_at']:
            shop['created_at'] = shop['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if shop['updated_at']:
            shop['updated_at'] = shop['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        return jsonify({
            'code': 0,
            'message': '店铺初始化成功',
            'data': {
                'shop_id': shop['shop_id'],
                'agent_id': shop['agent_id'],
                'agent_account': shop['agent_account'],
                'sender_uid': shop['sender_uid'],
                'device_id': shop['device_id']
            }
        })
        
    except Exception as e:
        print(f"店铺初始化失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'初始化失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@shop_bp.route('/', methods=['GET'])
def get_shops():
    """获取当前代理的店铺列表"""
    connection = None
    cursor = None
    try:
        from flask import session
        agent_id = session.get('agent_id')
        if not agent_id:
            return jsonify({'code': 1, 'message': '未登录', 'data': None}), 401
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        query = """
        SELECT s.*, a.agent_account as agent_name
        FROM shops s
        LEFT JOIN agents a ON s.agent_id = a.id
        WHERE s.agent_id = %s
        ORDER BY s.created_at DESC
        """
        cursor.execute(query, [agent_id])
        shops = cursor.fetchall()
        # 处理时间格式和类型
        for shop in shops:
            if shop['created_at']:
                shop['created_at'] = shop['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            if shop['updated_at']:
                shop['updated_at'] = shop['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
            if shop.get('bind_time'):
                shop['bind_time'] = shop['bind_time'].strftime('%Y-%m-%d %H:%M:%S')
            if shop.get('expire_time'):
                shop['expire_time'] = shop['expire_time'].strftime('%Y-%m-%d %H:%M:%S')
            # 保证开关字段为int类型
            for k in ['auto_reply','auto_comment','auto_meal','ck_status']:
                if k in shop:
                    shop[k] = int(shop[k]) if shop[k] is not None else 0
        return jsonify({'code': 0, 'message': '获取成功', 'data': shops})
    except Exception as e:
        print(f"获取店铺列表失败: {str(e)}")
        return jsonify({'code': 1, 'message': f'获取失败: {str(e)}', 'data': None}), 500
    finally:
        if cursor: cursor.close()
        if connection: connection.close()

@shop_bp.route('/<int:shop_id>', methods=['GET'])
def get_shop(shop_id):
    """获取单个店铺信息"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        query = """
        SELECT s.*, a.agent_account as agent_name
        FROM shops s
        LEFT JOIN agents a ON s.agent_id = a.id
        WHERE s.id = %s
        """
        cursor.execute(query, [shop_id])
        shop = cursor.fetchone()
        
        if not shop:
            return jsonify({
                'code': 1,
                'message': '店铺不存在',
                'data': None
            }), 404
        
        # 处理时间格式
        if shop['created_at']:
            shop['created_at'] = shop['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if shop['updated_at']:
            shop['updated_at'] = shop['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        return jsonify({
            'code': 0,
            'message': '获取成功',
            'data': shop
        })
    except Exception as e:
        print(f"获取店铺信息失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'获取失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@shop_bp.route('/<int:shop_id>', methods=['PUT'])
def update_shop(shop_id):
    """更新店铺信息"""
    connection = None
    cursor = None
    try:
        data = request.get_json()
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查店铺是否存在
        check_query = "SELECT id FROM shops WHERE id = %s"
        cursor.execute(check_query, [shop_id])
        existing_shop = cursor.fetchone()
        
        if not existing_shop:
            return jsonify({
                'code': 1,
                'message': '店铺不存在',
                'data': None
            }), 404
        
        # 构建更新查询
        update_fields = []
        update_values = []
        
        fields_to_update = [
            'login_account', 'shop_name', 'remark', 'logo', 'cookie',
            'ck_status', 'auto_reply', 'auto_comment', 'auto_meal', 'agent_account',
            'auto_meal_periods',
            'reply_message_text', 'auto_comment_types', 'comment_good_text', 'comment_mid_text', 'comment_bad_text'  # 支持自动回复和回评自定义
        ]
        
        for field in fields_to_update:
            if field in data:
                update_fields.append(f"{field} = %s")
                update_values.append(data[field])
        
        if not update_fields:
            return jsonify({
                'code': 1,
                'message': '没有提供有效的更新字段',
                'data': None
            }), 400
        
        # 添加更新时间
        update_fields.append("updated_at = NOW()")
        
        # 执行更新
        update_query = f"UPDATE shops SET {', '.join(update_fields)} WHERE id = %s"
        update_values.append(shop_id)
        
        cursor.execute(update_query, update_values)
        connection.commit()
        
        # 获取更新后的店铺信息
        cursor.execute("SELECT * FROM shops WHERE id = %s", [shop_id])
        shop = cursor.fetchone()
        
        # 处理时间格式
        if shop['bind_time']:
            shop['bind_time'] = shop['bind_time'].strftime('%Y-%m-%d %H:%M:%S')
        if shop['expire_time']:
            shop['expire_time'] = shop['expire_time'].strftime('%Y-%m-%d %H:%M:%S')
        if shop['created_at']:
            shop['created_at'] = shop['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        if shop['updated_at']:
            shop['updated_at'] = shop['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        return jsonify({
            'code': 0,
            'message': '更新成功',
            'data': shop
        })
    except Exception as e:
        print(f"更新店铺失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'更新失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@shop_bp.route('/<int:shop_id>', methods=['DELETE'])
def delete_shop(shop_id):
    """删除店铺"""
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查店铺是否存在
        check_query = "SELECT id FROM shops WHERE id = %s"
        cursor.execute(check_query, [shop_id])
        existing_shop = cursor.fetchone()
        
        if not existing_shop:
            return jsonify({
                'code': 1,
                'message': '店铺不存在',
                'data': None
            }), 404
        
        # 执行删除
        delete_query = "DELETE FROM shops WHERE id = %s"
        cursor.execute(delete_query, [shop_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '删除成功',
            'data': None
        })
    except Exception as e:
        print(f"删除店铺失败: {str(e)}")
        if connection:
            connection.rollback()
        return jsonify({
            'code': 1,
            'message': f'删除失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@shop_bp.route('/<int:shop_id>/renew', methods=['POST'])
def renew_shop(shop_id):
    """店铺续费接口，消耗代理积分，延长到期时间"""
    from flask import session
    connection = None
    cursor = None
    try:
        agent_id = session.get('agent_id')
        if not agent_id:
            return jsonify({'code': 1, 'message': '未登录', 'data': None}), 401
        data = request.get_json() or {}
        points = int(data.get('points', 1))
        if points < 1:
            points = 1
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        # 查询店铺和代理信息
        cursor.execute('SELECT * FROM shops WHERE id = %s AND agent_id = %s', [shop_id, agent_id])
        shop = cursor.fetchone()
        if not shop:
            return jsonify({'code': 1, 'message': '店铺不存在', 'data': None}), 404
        cursor.execute('SELECT * FROM agents WHERE id = %s', [agent_id])
        agent = cursor.fetchone()
        if not agent:
            return jsonify({'code': 1, 'message': '代理不存在', 'data': None}), 404
        if agent['balance'] < points:
            return jsonify({'code': 1, 'message': '积分不足', 'data': None}), 400
        # 计算新到期时间
        from datetime import datetime, timedelta
        now = datetime.now()
        expire_time = shop.get('expire_time')
        if expire_time and isinstance(expire_time, datetime):
            base_time = expire_time if expire_time > now else now
        elif expire_time:
            try:
                base_time = datetime.strptime(expire_time, '%Y-%m-%d %H:%M:%S')
                if base_time < now:
                    base_time = now
            except:
                base_time = now
        else:
            base_time = now
        new_expire_time = base_time + timedelta(days=31*points)
        # 扣除积分，更新店铺到期时间
        cursor.execute('UPDATE agents SET balance = balance - %s WHERE id = %s', [points, agent_id])
        cursor.execute('UPDATE shops SET expire_time = %s, updated_at = NOW() WHERE id = %s', [new_expire_time, shop_id])
        connection.commit()
        return jsonify({'code': 0, 'message': '续费成功', 'data': {'new_expire_time': new_expire_time.strftime('%Y-%m-%d %H:%M:%S')}})
    except Exception as e:
        if connection:
            connection.rollback()
        print(f'店铺续费失败: {str(e)}')
        return jsonify({'code': 1, 'message': f'续费失败: {str(e)}', 'data': None}), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@shop_bp.route('/<int:shop_id>/logs', methods=['GET'])
def get_shop_logs(shop_id):
    """获取店铺日志"""
    connection = None
    cursor = None
    try:
        from flask import session
        agent_id = session.get('agent_id')
        if not agent_id:
            return jsonify({'code': 1, 'message': '未登录', 'data': None}), 401
        
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        # 检查店铺是否属于当前代理
        cursor.execute('SELECT id FROM shops WHERE id = %s AND agent_id = %s', [shop_id, agent_id])
        shop = cursor.fetchone()
        if not shop:
            return jsonify({'code': 1, 'message': '店铺不存在或无权限', 'data': None}), 404
        
        # 获取查询参数
        task_type = request.args.get('task_type')
        
        # 构建查询条件
        where_conditions = ["shop_id = %s"]
        query_params = [shop_id]
        
        if task_type:
            where_conditions.append("task_type = %s")
            query_params.append(task_type)
        
        # 获取店铺日志，按时间倒序排列
        query = f"""
        SELECT id, shop_id, shop_name, task_type, log_message, created_at
        FROM shop_logs 
        WHERE {' AND '.join(where_conditions)}
        ORDER BY created_at DESC 
        LIMIT 100
        """
        cursor.execute(query, query_params)
        logs = cursor.fetchall()
        
        # 处理时间格式
        for log in logs:
            if log['created_at']:
                log['created_at'] = log['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        return jsonify({
            'code': 0,
            'message': '获取成功',
            'data': logs
        })
    except Exception as e:
        print(f"获取店铺日志失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'获取失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 

@shop_bp.route('/<int:shop_id>/expire', methods=['PUT'])
def update_shop_expire(shop_id):
    """编辑店铺过期时间（仅root角色可操作）"""
    connection = None
    cursor = None
    try:
        from flask import session
        agent_id = session.get('agent_id')
        if not agent_id:
            return jsonify({'code': 1, 'message': '未登录', 'data': None}), 401
        
        # 检查当前用户是否为root角色
        connection = get_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute('SELECT role FROM agents WHERE id = %s', [agent_id])
        agent = cursor.fetchone()
        
        if not agent or agent['role'] != 'root':
            return jsonify({'code': 1, 'message': '权限不足，只有管理员可以编辑过期时间', 'data': None}), 403
        
        data = request.get_json()
        if not data or 'expire_time' not in data:
            return jsonify({'code': 1, 'message': '缺少过期时间参数', 'data': None}), 400
        
        # 检查店铺是否存在
        cursor.execute('SELECT id FROM shops WHERE id = %s', [shop_id])
        shop = cursor.fetchone()
        if not shop:
            return jsonify({'code': 1, 'message': '店铺不存在', 'data': None}), 404
        
        # 更新过期时间
        cursor.execute('UPDATE shops SET expire_time = %s, updated_at = NOW() WHERE id = %s', 
                     [data['expire_time'], shop_id])
        connection.commit()
        
        return jsonify({
            'code': 0,
            'message': '过期时间更新成功',
            'data': {'expire_time': data['expire_time']}
        })
        
    except Exception as e:
        if connection:
            connection.rollback()
        print(f"更新店铺过期时间失败: {str(e)}")
        return jsonify({
            'code': 1,
            'message': f'更新失败: {str(e)}',
            'data': None
        }), 500
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 